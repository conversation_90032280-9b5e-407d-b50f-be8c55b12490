{"part_number": "TPS5410-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS5410-Q1 1A 宽输入范围 Swift™ 降压转换器", "features": ["符合汽车应用要求", "宽输入电压范围: 5.5V 至 36V", "高达 1A 的连续 (1.2A 峰值) 输出电流", "通过 110mΩ 集成式 MOSFET 开关实现高达 95% 的高效率", "宽输出电压范围: 可调节为低至 1.22V，初始精度为 1.5%", "内部补偿可最大限度减少外部器件数量", "适用于小型滤波器尺寸的固定 500kHz 开关频率", "通过输入电压前馈改进线路稳压和瞬态响应", "系统受过流限制、过压保护和热关断的保护", "-40℃ 至 125℃ 的工作结温范围", "采用小型 8 引脚 SOIC 封装", "如需 SWIFT 文档、应用手册和设计软件，请访问 TI 网站: www.ti.com.cn/swift"], "description": "作为 SWIFT™ 直流/直流稳压器系列的一员，TPS5410 是一款高输出电流 PWM 转换器，集成了低电阻高侧 N 沟道 MOSFET。具有所列特性的基板上还包括高性能电压误差放大器（可在瞬态条件下提供高稳压精度）、欠压锁定电路（用于防止在输入电压达到 5.5V 前启动）、内部设置的慢启动电路（用于限制浪涌电流）以及电压前馈电路（用于改进瞬态响应）。通过使用 ENA 引脚，关断电源电流通常可减少到 18µA。其他特性包括高电平有效使能端、过流限制、过压保护和热关断。为降低设计复杂性并减少外部元件数量，TPS5410 反馈环路进行了内部补偿。TPS5410 器件采用易于使用的 8 引脚 SOIC 封装。TI 提供评估模块和软件工具，有助于快速实现高性能电源设计，可满足迫切的设备开发周期要求。", "applications": ["消费类: 机顶盒、DVD、LCD 显示屏", "工业用和车载音频电源", "电池充电器、大功率 LED 电源", "12V/24V 分布式电源系统"], "ordering_information": [{"part_number": "TPS5410-Q1", "order_device": "TPS5410QDRQ1", "status": "Active", "package_type": "SOIC", "package_code": "D", "carrier_description": "Tape & Reel", "carrier_quantity": 2500, "package_drawing_code": "D0008A", "marking": "5410Q", "pin_count": 8, "length": 5.0, "width": 6.19, "height": 1.75, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "未找到", "application_grade": "Automotive"}, {"part_number": "TPS5410-Q1", "order_device": "TPS5410QDRQ1.A", "status": "Active", "package_type": "SOIC", "package_code": "D", "carrier_description": "Tape & Reel", "carrier_quantity": 2500, "package_drawing_code": "D0008A", "marking": "5410Q", "pin_count": 8, "length": 5.0, "width": 6.19, "height": 1.75, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "未找到", "application_grade": "Automotive"}], "typical_application_circuit": "是", "pin_config": "是", "function_block_diagram": "是", "pin_function": [{"product_part_number": "TPS5410-Q1", "package_type": "SOIC", "pins": [{"pin_number": "1", "pin_name": "BOOT", "pin_description": "高侧 FET 栅极驱动器的升压电容器。在 BOOT 引脚和 PH 引脚之间连接一个 0.01µF 的低 ESR 电容器。"}, {"pin_number": "2, 3", "pin_name": "NC", "pin_description": "内部未连接"}, {"pin_number": "4", "pin_name": "VSENSE", "pin_description": "稳压器的反馈电压。连接到输出分压器。"}, {"pin_number": "5", "pin_name": "ENA", "pin_description": "开/关控制。低于 0.5V 时，器件停止开关。浮空该引脚以使能。"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "接地。"}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "输入电源电压。使用高质量、低 ESR 的陶瓷电容器将 VIN 引脚旁路到靠近器件封装的 GND 引脚。"}, {"pin_number": "8", "pin_name": "PH", "pin_description": "高侧功率 MOSFET 的源极。连接到外部电感和二极管。"}]}], "datasheet_cn": {"datasheet_name": "TPS5410-Q1 1A 宽输入范围 Swift™ 降压转换器", "datasheet_path": "用户上传文件", "release_date": "2024-01", "version": "ZHCSQS4A"}, "datasheet_en": {"datasheet_name": "TPS5410-Q1 1-A, Wide Input Range, Step-Down Swift™ Converter", "datasheet_path": "SLVSA23", "release_date": "2024-01", "version": "REVISED JANUARY 2024"}, "family_comparison": "未找到", "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 36, "min_input_voltage": 5.5, "max_output_voltage": "未找到", "min_output_voltage": 1.22, "max_output_current": 1, "max_switch_frequency": 0.6, "quiescent_current": 2000, "high_side_mosfet_resistance": 100, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 1.55, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "不适用", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1.5, "output_reference_voltage": 1.221, "loop_control_mode": "电压模式", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "5.5V", "max_output_voltage": "Adjustable", "min_output_voltage": "1.22V", "max_output_current": "1A", "max_switch_frequency": "600kHz", "quiescent_current": "2mA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "1.55A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1.5%", "output_reference_voltage": "1.221V", "loop_control_mode": "电压模式"}}