{"part_number": "MP8619", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "EOL", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片", "part_number_title": "8A, 25V, 600kHz Synchronous Step-down Converter", "features": ["Wide 4.5V to 25V Operating Input Range", "8A Output Current", "Proprietary Switching Loss Reduction Technique", "Fixed 600kHz Switching Frequency", "Sync from 300kHz to 2MHz External Clock", "Internal Compensation", "OCP Protection and Thermal Shutdown", "Output Adjustable from 0.8V to 15V", "Available in 30-pin 5x6mm QFN30 Package"], "description": "The MP8619 is a high frequency synchronous rectified step-down switch mode converter with built in internal power MOSFETs. It offers a very compact solution to achieve 8A continuous output current over a wide input supply range with excellent load and line regulation. Current mode operation provides fast transient response and eases loop stabilization. Full protection features include OCP and thermal shut down. The MP8619 requires a minimum number of readily available standard external components and is available in a space saving 5mm x 6mm 30-pin QFN package.", "applications": ["Networking Systems", "Digital Set Top Boxes", "Distributed Power Systems"], "ordering_information": [{"part_number": "MP8619", "order_device": "MP8619EQJ", "status": "EOL", "package_type": "QFN30", "package_code": "QFN30", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": null, "marking": "8619EQJ", "pin_count": 30, "length": 6, "width": 5, "height": null, "pitch": 0.5, "min_operation_temp": -20, "max_operation_temp": 85, "output_voltage": null, "application_grade": "Industrial"}], "typical_application_circuit": "Page 1, TYPICAL APPLICATION", "pin_config": "Page 2, PACKAGE REFERENCE", "function_block_diagram": "Page 7, Figure 2—Function Block Diagram", "pin_function": [{"product_part_number": "MP8619", "package_type": "QFN30", "pins": [{"pin_number": "1–4", "pin_name": "NC", "pin_description": "No Connect."}, {"pin_number": "5–8", "pin_name": "BG", "pin_description": "Low Side Gate Drive"}, {"pin_number": "9–15, 20,21, 30, Exposed Pad", "pin_name": "GND", "pin_description": "System Ground. This pin is the reference ground of the regulated output voltage. For this reason care must be taken in PCB layout. Connect exposed pad to GND plane for optimal thermal performance."}, {"pin_number": "16–19", "pin_name": "SW", "pin_description": "Switch Output. Use wide PCB traces and multiple vias to make the connection."}, {"pin_number": "22", "pin_name": "VCC", "pin_description": "Bias Supply. Decouple with 1µF capacitor."}, {"pin_number": "23", "pin_name": "BST", "pin_description": "Bootstrap. A capacitor connected between SW and BS pins is required to form a floating supply across the high-side switch driver."}, {"pin_number": "24", "pin_name": "FB", "pin_description": "Feedback. An external resistor divider from the output to GND, tapped to the FB pin, sets the output voltage."}, {"pin_number": "25", "pin_name": "PG", "pin_description": "Power Good Output, the output of this pin is open drain. Power good threshold is 90% low to high and 70% high to low of regulation value. There is a 20µs delay to pull PG if the output voltage is lower than 10% of regulation value."}, {"pin_number": "26", "pin_name": "EN/SYNC", "pin_description": "EN=1 to enable the MP8619. External clock can be applied to EN pin for changing switching frequency. For automatic start-up, connect EN pin to VIN with 100kΩ resistor."}, {"pin_number": "27–29", "pin_name": "IN", "pin_description": "Supply Voltage. The MP8619 operates from a +4.5V to +25V input rail. C1 is needed to decouple the input rail. Use wide PCB traces and multiple vias to make the connection."}]}], "datasheet_en": {"name": "MP8619 Rev. 0.9", "path": "current_file", "release_date": "2010-01-20", "version": "0.9"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "25V", "min_input_voltage": "4.5V", "max_output_voltage": "15V", "min_output_voltage": "0.8V", "max_output_current": "8A", "max_switch_frequency": "0.73MHz", "quiescent_current": "700µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "8mΩ", "over_current_protection_threshold": "9.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "未找到", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "0.808V", "loop_control_mode": "峰值电流模式"}, "extraction_metadata": {"extractor": "prompt_pdf_optimized", "version": "5.0", "extraction_timestamp": "2024-05-16T12:30:00Z"}}