{"part_number": "TPS543A26", "manufacturer": "Texas Instruments", "country": "未找到", "manufacturing_status": "Active", "application_grade": "未找到", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS543A26 具有内部补偿高级电流模式控制功能的 4V 至 18V 输入、16A SWIFT™ 同步降压转换器", "features": ["固定频率、内部补偿、高级电流模式 (ACM) 控制", "集成 6.5mΩ 和 2mΩ MOSFET", "输入电压范围为 4V 至 18V", "0.5V 至 7V 输出电压范围", "真差分遥感放大器 (RSA)", "三种可选的 PWM 斜坡选项，可优化控制环路性能", "五种可选的开关频率：500kHz、750kHz、1MHz、1.5MHz 和 2.2MHz", "与一个外部时钟同步", "0.5V，整个温度范围内的电压基准精度为 ±0.5%", "1ms、2ms、4ms 和 8ms 可选软启动时间", "单调启动至预偏置输出", "可选的电流限制，支持 16A 和 12A 运行", "具有可调节输入欠压锁定功能的使能端", "电源正常输出监视器", "输出过压、输出欠压、输入欠压、过流和过热保护", "-40℃ 至 150℃ 的工作结温范围", "2.5mm x 4.5mm，17 引脚 WQFN-HR 封装，间距为 0.5mm", "无铅 (符合 RoHS 标准)", "与以下器件引脚兼容：TPS543B22 和 TPS543A22", "采用热增强型封装 TPS543B25T (快插兼容)", "可提供 SIMPLIS 模型", "使用 TPS543A26 并借助 WEBENCH® Power Designer 创建定制设计方案"], "description": "TPS543A26 是一款高效率 18V、16A 同步降压转换器，采用内部补偿的定频高级电流模式 (ACM) 控制架构，可在处于 FCCM 模式长期运行的同时，产生 0.5V 至 7V 的输出电压。该器件可提供高效率，且运行时的开关频率高达 2.2MHz，使得该器件适用于需要小巧解决方案尺寸的设计。固定频率控制器可以在 500kHz 至 2.2MHz 范围内运行，并且可以通过 SYNC 引脚与外部时钟同步。其他功能包括高精度电压基准、双线遥感、可选软启动时间、单调启动至预偏置输出、可选电流限制、可调 UVLO (通过 EN 引脚实现) 以及全套故障保护。", "applications": ["无线和有线通信基础设施设备", "光纤网络", "测试和测量", "医疗和保健"], "ordering_information": [{"part_number": "TPS543A26", "order_device": "TPS543A26RYSR", "status": "Active", "package_type": "WQFN-FCRLF", "package_code": "RYS", "carrier_description": "LARGE T&R", "carrier_quantity": "5000", "package_drawing_code": "RYS0017A", "marking": "T543A26", "pin_count": "17", "length": "4.5", "width": "2.5", "height": "0.7", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "150", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS543A26", "order_device": "TPS543A26RYSR.A", "status": "Active", "package_type": "WQFN-FCRLF", "package_code": "RYS", "carrier_description": "LARGE T&R", "carrier_quantity": "5000", "package_drawing_code": "RYS0017A", "marking": "T543A26", "pin_count": "17", "length": "4.5", "width": "2.5", "height": "0.7", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "150", "output_voltage": "未找到", "application_grade": "未找到"}], "typical_application_circuit": "是", "pin_config": "是", "function_block_diagram": "是", "pin_function": [{"product_part_number": "TPS543A26", "package_type": "WQFN-FCRLF", "pins": [{"pin_number": "1", "pin_name": "AGND", "pin_description": "内部模拟电路的接地回路"}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "模拟控制电路的电源。将一个 10Ω 电阻从 VDRV 连接到此引脚，并使用一个 0.1μF 电容器旁路至 AGND。"}, {"pin_number": "3", "pin_name": "VDRV", "pin_description": "内部 5V 稳压器输出以及到驱动器的内部连接。通过 2.2μF 陶瓷电容器将这些引脚旁路至 PGND。请参阅节 6.3.2"}, {"pin_number": "4, 9", "pin_name": "VIN", "pin_description": "功率级的输入功率。这些引脚到 PGND 的低阻抗旁路至关重要。需要靠近 IC 在每个 VIN 和 PGND 之间连接一个 1μF 电容器。"}, {"pin_number": "5, 8, 16, 17", "pin_name": "PGND", "pin_description": "功率级接地回路。此引脚在内部连接到低边 MOSFET 的源极。"}, {"pin_number": "6", "pin_name": "SW", "pin_description": "转换器的开关节点。将该引脚连接到输出电感器。"}, {"pin_number": "7", "pin_name": "BOOT", "pin_description": "内部高侧 MOSFET 栅极驱动器的电源。在此引脚和 SW 之间连接一个电容器。"}, {"pin_number": "10", "pin_name": "EN", "pin_description": "启用引脚。悬空或拉高以启用，或使用外部信号启用和禁用，或使用电阻分压器调整输入欠压锁定。请参阅节 6.3.3"}, {"pin_number": "11", "pin_name": "PG", "pin_description": "开漏电源正常指示器。请参阅节 6.3.10"}, {"pin_number": "12", "pin_name": "SYNC/FSEL", "pin_description": "频率选择和外部时钟同步。接地电阻设置器件的开关频率。还可以在该引脚上应用外部时钟以同步开关频率。请参阅节 *******"}, {"pin_number": "13", "pin_name": "MSEL", "pin_description": "接地电阻可用于选择电流限制、软启动速率和 PWM 斜坡幅度。请参阅节 6.3.9"}, {"pin_number": "14", "pin_name": "GOSNS", "pin_description": "接地检测返回并输入到差分远程检测放大器"}, {"pin_number": "15", "pin_name": "FB", "pin_description": "反馈引脚和差分远程检测放大器的输入，用于输出电压调节。将此引脚连接到电阻分压器的中点以设置输出电压。请参阅节 6.3.6"}]}], "datasheet_cn": {"name": "TPS543A26", "path": "ZHCSNZ7A", "date": "2024-02", "version": "A"}, "datasheet_en": {"name": "TPS543A26", "path": "SLVSGC8", "date": "未找到", "version": "未找到"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "18", "min_input_voltage": "4", "max_output_voltage": "7", "min_output_voltage": "0.5", "max_output_current": "16", "max_switch_frequency": "2.2", "quiescent_current": "1200", "high_side_mosfet_resistance": "6.5", "low_side_mosfet_resistance": "2", "over_current_protection_threshold": "23/18", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "0.5", "output_reference_voltage": "0.5", "loop_control_mode": "平均电流模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "4V", "max_output_voltage": "7V", "min_output_voltage": "0.5V", "max_output_current": "16A", "max_switch_frequency": "2.2MHz", "quiescent_current": "1200μA", "high_side_mosfet_resistance": "6.5mΩ", "low_side_mosfet_resistance": "2mΩ", "over_current_protection_threshold": "17.5-23A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "FCCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±0.5%", "output_reference_voltage": "0.5V", "loop_control_mode": "峰值电流模式"}}