{"part_number": "TPS546A24A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片(<PERSON>)", "part_number_title": "TPS546A24A 2.95V 至 18V、10A、高达 4× 可堆叠、PMBus® 降压转换器", "features": ["支持双电源：2.95V 至 18V PVIN；2.95V 至 18V AVIN（4VIN VDD5 开关电压）", "集成 5.5mΩ/1.8mΩ MOSFET", "具有可选内部补偿的平均电流模式控制", "2×、3×、4× 可堆叠，电流共享高达 40A，每个输出可支持单个地址", "通过引脚搭接的可选输出电压范围为 0.5V 至 5.5V，使用 PMBus VOUT_COMMAND 的电压范围为 0.25V 至 5.5V", "广泛的 PMBus 命令集，可遥测 VOUT、IOUT 和内部裸片温度", "通过内部反馈分压器实现差分遥感，可检测到小于 1% 的 VOUT 误差，TJ 为 –40°C 至 +150°C", "通过 PMBus 实现 AVS 和裕量调节", "采用 MSEL 引脚，引脚编程 PMBus 默认值", "12 种介于 225kHz 和 1.5MHz 之间的可选开关频率（8 个引脚搭接选项）", "频率同步输入/同步输出", "支持预偏置输出", "支持强耦合电感器", "7mm × 5mm × 1.5mm、40 引脚 QFN、间距 = 0.5mm", "使用 TPS546A24A 并借助 WEBENCH® Power Designer 创建定制设计"], "description": "TPS546D24A 是一款高度集成的非隔离式直流/直流转换器，具有较高的工作频率和 40A 的电流输出，采用 7mm × 5mm 封装。可将两个、三个和四个 TPS546D24A 器件互连，在单个输出上提供最高 160A 的电流。该器件可通过 VDD5 引脚，利用 5V 的外部电源对内部的 5V LDO 进行过驱动，以提高效率并降低转换器的功耗。\nTPS546D24A 使用专有的固定频率电流模式控制，具有输入前馈和可选的内部补偿元件，可在各种输出电容下最大限度减小尺寸和提高稳定性。\nPMBus 接口具有 1MHz 时钟支持，为转换器配置提供了便捷且标准化的数字接口，并且实现了对输出电压、输出电流和内部裸片温度等关键参数的监控。对故障状况的响应可设置为重新启动、锁存或忽略，具体取决于系统要求。堆叠器件之间的反向通道通信使得所有 TPS546D24A 转换器能够为单个输出轨供电，以共享一个地址，从而简化系统软件/固件设计。也可通过 BOM 选择在不进行 PMBus 通信的情况下，配置输出电压、开关频率、软启动时间和过流故障限制等关键参数，以支持无程序加电。", "applications": ["数据中心交换机、机架式服务器", "有源天线系统、远程射频和基带单元", "自动化测试设备、CT、PET 和 MRI", "ASIC、SoC、FPGA、DSP 内核和 I/O 电压"], "ordering_information": [{"part_number": "TPS546A24A", "order_device": "TPS546A24ARVFR", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "package_drawing_code": "RVF0040A", "marking": "TPS546A24A", "pin_count": 40, "length": 7.0, "width": 5.0, "height": 1.52, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 150, "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546A24A", "order_device": "TPS546A24ARVFR.A", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "package_drawing_code": "RVF0040A", "marking": "TPS546A24A", "pin_count": 40, "length": 7.0, "width": 5.0, "height": 1.52, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 150, "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546A24A", "order_device": "TPS546A24ARVFR.B", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "package_drawing_code": "RVF0040A", "marking": "TPS546A24A", "pin_count": 40, "length": 7.0, "width": 5.0, "height": 1.52, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 150, "output_voltage": "未找到", "application_grade": "未找到"}], "typical_application_circuit": "Page 148, 图 8-1. TPS546A24A 应用", "pin_config": "Page 3, 图 5-1. 带有外露散热焊盘的 40 引脚 LQFN-CLIP RVF 封装（俯视图）", "function_block_diagram": "Page 17, 7.2 功能方框图", "pin_function": [{"product_part_number": "TPS546A24A", "package_type": "LQFN-CLIP", "pins": [{"pin_number": "1", "pin_name": "PGD/RST_B", "pin_description": "开漏电源正常状态或 (21h) VOUT_COMMAND RESET#。由 (EDh) MFR_SPECIFIC_29 (MISC_OPTIONS) 中的用户可编程 RESET# 位确定。默认引脚功能是开漏电源正常状态指示器。当配置为 RESET# 时，可以通过 (EDh) MFR_SPECIFIC_29 (MISC_OPTIONS) 中的 PULLUP# 位启用或禁用内部上拉。"}, {"pin_number": "2", "pin_name": "PMB_DATA", "pin_description": "PMBus DATA 引脚。请参阅电流 PMBus 规格。"}, {"pin_number": "3", "pin_name": "PMB_CLK", "pin_description": "PMBus CLK 引脚。请参阅电流 PMBus 规格。"}, {"pin_number": "4", "pin_name": "BP1V5", "pin_description": "1.5V 内部稳压器的输出。该稳压器为数字电路供电，应使用额定电压至少为 6V 的 X5R 或更好的陶瓷电容器以至少 1µF 的电容旁路至 DRTN。BP1V5 并非设计用于为外部电路供电。"}, {"pin_number": "5", "pin_name": "DRTN", "pin_description": "BP1V5 旁路电容器的数字旁路回路。在内部连接到 AGND。不要连接到 PGND 或 AGND。"}, {"pin_number": "6", "pin_name": "SMB_ALRT", "pin_description": "SMBus 警报引脚。请参阅 SMBus 规范。"}, {"pin_number": "7", "pin_name": "BOOT", "pin_description": "内部飞跨高侧驱动器的自动加载 (bootstrap) 引脚。在该引脚与 SW 之间连接一个额定电压至少为 10V 的典型 100nF X5R 或更好陶瓷电容器。为了降低 SW 上的电压尖峰，可以将一个高达 8Ω 的可选 BOOT 电阻器与 BOOT 电容器串联放置，来减缓高侧 FET 的导通。"}, {"pin_number": "8-13", "pin_name": "SW", "pin_description": "已切换器件的电源输出。将输出平均滤波器和自动加载连接到此组引脚。"}, {"pin_number": "14-20", "pin_name": "PGND", "pin_description": "功率级接地回路。这些引脚在内部连接到散热焊盘。"}, {"pin_number": "21-25", "pin_name": "PVIN", "pin_description": "功率级的输入功率。这些引脚到 PGND 的低阻抗旁路至关重要。PVIN 至 PGND 应使用额定电压至少为最大 PVIN 电压 1.5 倍的 X5R 或更好陶瓷电容器旁路。此外，至少应将一个额定电压至少为最大 PVIN 电压 1.5 倍的 0402 2.2nF - 10nF X7R 或更好陶瓷电容放置在靠近 PVIN 和 PGND 引脚或 PVIN 引脚的位置，来降低高频旁路阻抗。"}, {"pin_number": "26", "pin_name": "AVIN", "pin_description": "控制器的输入电源。使用额定值至少为连接到 AGND 最大 AVIN 电压的 1.5 倍的最小 1µF X5R 或更好陶瓷电容器旁路。如果 AVIN 连接到与 PVIN 或 VDD5 相同的输入，建议在 PVIN 或 VDD5 与 AVIN 之间使用一个最小 10µs 的 R-C 滤波器，来降低 AVIN 上的开关噪声。"}, {"pin_number": "27", "pin_name": "EN/UVLO", "pin_description": "使能开关作为 PMBus 控制引脚。EN/UVLO 还可连接到电阻分压器，以对输入电压 UVLO 进行编程。"}, {"pin_number": "28", "pin_name": "VDD5", "pin_description": "5V 内部稳压器的输出。该稳压器为控制器的驱动级供电，应使用额定值至少为 10V 至热焊盘上的 PGND 的 4.7µF X5R 或更好的陶瓷电容器进行旁路。此引脚到 PGND 的低阻抗旁路至关重要。"}, {"pin_number": "29", "pin_name": "MSEL2", "pin_description": "将该引脚连接到 BP1V5 和 AGND 之间容差为 1% 或更佳的电阻分压器，来获得不同的软启动时间、过流故障限制和多相信息选项。"}, {"pin_number": "30", "pin_name": "VSEL", "pin_description": "针对内部电压反馈分压器和默认输出电压的不同选项，将此引脚连接到 BP1V5 和 AGND 之间的容差为 1% 或更佳的电阻分压器。请参阅对 VSEL 进行编程"}, {"pin_number": "31", "pin_name": "ADRSEL", "pin_description": "针对不同的 PMBus 地址和频率同步选项（包括确定 SYNC 引脚为 SYNC IN 或 SYNC OUT 功能），将此引脚连接到 BP1V5 和 AGND 之间的容差为 1% 或更佳的电阻分压器。请参阅对 ADRSEL 进行编程"}, {"pin_number": "32", "pin_name": "MSEL1", "pin_description": "针对开关频率和内部补偿参数的不同选项，将此引脚连接到 BP1V5 和 AGND 之间的容差为 1% 或更佳的电阻分压器。请参阅对 MSEL1 进行编程"}, {"pin_number": "33", "pin_name": "VOSNS", "pin_description": "遥感放大器的正输入。对于采用多相配置的独立器件或环路主器件，请将 VOSNS 引脚连接到负载的输出电压。对于采用多相配置的环路从器件，输出电压检测或调节不需要遥感放大器，该引脚可保持悬空。"}, {"pin_number": "34", "pin_name": "GOSNS/SLAVE", "pin_description": "环路主器件的遥感放大器的负输入或应上拉为高电平来指示环路从器件。对于采用多相配置的独立器件或环路主器件，请将 GOSNS 引脚连接到负载处的接地端。对于采用多相位配置的环路从器件，必须将 GOSNS 引脚上拉至 BP1V5，来指示器件为环路从器件。"}, {"pin_number": "35", "pin_name": "VSHARE", "pin_description": "用于多相操作的电压共享信号。对于独立器件，VSHARE 引脚必须保持悬空。VSHARE 可通过高达 50pF 的电容旁路至 AGND。"}, {"pin_number": "36", "pin_name": "NC", "pin_description": "无内部连接。连接到散热焊盘上的 PGND。"}, {"pin_number": "37", "pin_name": "AGND", "pin_description": "控制器的模拟接地回路。将 AGND 引脚直接连接到 PCB 板上的散热焊盘。"}, {"pin_number": "38", "pin_name": "SYNC", "pin_description": "为了实现频率同步，可通过 ADRSEL 引脚或 (E4h) MFR_SPECIFIC_20 (SYNC_CONFIG) PMBus 命令将其编程为 SYNC IN 或 SYNC OUT 引脚。SYNC 引脚在不使用时可保持悬空。"}, {"pin_number": "39", "pin_name": "BCX_CLK", "pin_description": "用于堆叠器件之间的反向通道通信的时钟"}, {"pin_number": "40", "pin_name": "BCX_DAT", "pin_description": "用于堆叠器件之间的反向通道通信的数据"}, {"pin_number": "—", "pin_name": "散热焊盘", "pin_description": "封装散热焊盘，内部连接至 PGND。散热焊盘必须具有足够的焊接覆盖范围才能正常工作。"}]}], "datasheet_cn": {"datasheet_name": "ZHCSKX6A", "datasheet_path": "本地", "release_date": "2020-09", "version": "A"}, "datasheet_en": {"datasheet_name": "SLUSE16", "datasheet_path": "本地", "release_date": "未找到", "version": "未找到"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "2.95V", "max_output_voltage": "5.5V", "min_output_voltage": "0.25V", "max_output_current": "10A", "max_switch_frequency": "1.5MHz", "quiescent_current": "12500µA", "high_side_mosfet_resistance": "5.5mΩ", "low_side_mosfet_resistance": "1.8mΩ", "over_current_protection_threshold": "4-23A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "FCCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "Latch", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±0.8%", "output_reference_voltage": "0.4V", "loop_control_mode": "平均电流模式"}}