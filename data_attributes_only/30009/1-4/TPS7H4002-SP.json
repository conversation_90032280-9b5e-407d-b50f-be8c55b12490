{"part_number": "TPS7H4002-SP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "军用级", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS7H4002-SP 耐辐射保障 3V 至 5.5V 输入、3A 同步降压转换器", "features": ["辐射性能: - 耐辐射保障高达 TID 100krad(Si); - SEL、SEB 和 SEGR 对于 LET 的抗扰度 = 75MeV-cm²/mg; - SET 和 SEFI的 LET 特征值高达 75MeV-cm²/mg", "峰值效率: 96.9% (Vo = 2.5V)", "集成 50mΩ 和 35mΩ MOSFET", "电源轨: 3V 至 5.5V (输入电压)", "3A 最大输出电流", "灵活的开关频率选项: - 100kHz 至 1MHz 可调内部振荡器; - 外部同步功能: 100kHz 至 1MHz; - 可针对初级-次级应用将 SYNC 引脚配置为 500kHz 输出", "在温度、辐射以及线路和负载调节范围内提供 0.807V ±1.5% 的电压基准", "单调启动至预偏置输出", "通过外部电容器进行可调节的软启动", "用于电源定序的输入使能和电源正常输出", "针对欠压和过压问题的电源正常输出监控", "可调节输入欠压锁定 (UVLO)", "20 引脚超小热增强型 Ceramic Flatpack 封装 (HKH)，适用于航天应用"], "description": "TPS7H4002-SP 是一款耐辐射的 5.5V、3A 同步降压转换器，具有高效率并集成高侧和低侧 MOSFET，实现了针对小型设计的优化。通过采用电流模式控制来减少元件数量，同时选择高开关频率来缩小电感器尺寸，从而进一步节省空间。输出电压启动斜坡由 SS/TR 引脚控制，可实现独立电源运行，或者跟踪状态下的运行。此外，正确配置启用与开漏电源正常引脚也可实现电源排序。此外，TPS7H4002-SP 可配置为初级-次级模式以实现并联运行。高侧 FET 的逐周期电流限制可在过载情况下保护器件，并通过低侧电源限流防止电流失控，从而实现功能增强。此外，还提供关闭低侧 MOSFET 的低侧吸收电流限值，以防止过多的反向电流。当芯片温度超过热限值时，热关断会禁用此器件。", "applications": ["用于 FPGA、微控制器、数据转换器和 ASIC 的太空卫星负载点电源", "通信负载", "光学成像有效载荷"], "ordering_information": [{"part_number": "TPS7H4002-SP", "order_device": "5962R2021001VSC", "status": "Active", "package_type": "CFP", "package_code": "HKH", "carrier_description": "TUBE", "carrier_quantity": 25, "package_drawing_code": "HKH0020A", "marking": "5962R2021001VS C TPS7H4002HKH", "pin_count": 20, "length": 12.75, "width": 7.37, "height": 2.416, "pitch": 1.27, "min_operation_temp": -55, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "军用级"}, {"part_number": "TPS7H4002-SP", "order_device": "TPS7H4002HKH/EM", "status": "Active", "package_type": "CFP", "package_code": "HKH", "carrier_description": "TUBE", "carrier_quantity": 25, "package_drawing_code": "HKH0020A", "marking": "TPS7H4002HKH /EM EVAL ONLY", "pin_count": 20, "length": 12.75, "width": 7.37, "height": 2.416, "pitch": 1.27, "min_operation_temp": 25, "max_operation_temp": 25, "output_voltage": "可调", "application_grade": "工业级"}, {"part_number": "TPS7H4002-SP", "order_device": "5962R2021001V9A", "status": "Active", "package_type": "KGD", "package_code": "XCEPT", "carrier_description": "OTHER", "carrier_quantity": 25, "package_drawing_code": "未找到", "marking": "未找到", "pin_count": 0, "length": "未找到", "width": "未找到", "height": "未找到", "pitch": "未找到", "min_operation_temp": -55, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "军用级"}, {"part_number": "TPS7H4002-SP", "order_device": "TPS7H4002Y/EM", "status": "Active", "package_type": "KGD", "package_code": "XCEPT", "carrier_description": "OTHER", "carrier_quantity": 5, "package_drawing_code": "未找到", "marking": "未找到", "pin_count": 0, "length": "未找到", "width": "未找到", "height": "未找到", "pitch": "未找到", "min_operation_temp": 25, "max_operation_temp": 25, "output_voltage": "可调", "application_grade": "工业级"}], "typical_application_circuit": "是", "pin_config": "是", "function_block_diagram": "是", "pin_function": [{"product_part_number": "TPS7H4002-SP", "package_type": "HKH", "pins": [{"pin_number": "1", "pin_name": "GND", "pin_description": "控制电路的返回端。"}, {"pin_number": "2", "pin_name": "EN", "pin_description": "EN 引脚在内部上拉，允许该引脚悬空以使能器件。使用两个电阻器调节输入欠压锁定 (UVLO)。"}, {"pin_number": "3", "pin_name": "RT", "pin_description": "在内部振荡模式下，在 RT 引脚和 GND 之间连接一个电阻器来设置开关频率。使此引脚悬空可将内部开关频率设置为 500 kHz。"}, {"pin_number": "4", "pin_name": "SYNC", "pin_description": "可选的 100kHz 至 1MHz 外部系统时钟输入。"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "开关稳压器控制电路的输入电源。"}, {"pin_number": "6, 7", "pin_name": "PVIN", "pin_description": "开关稳压器输出级的输入电源。"}, {"pin_number": "8, 9, 10", "pin_name": "PGND", "pin_description": "低侧功率 MOSFET 的返回端。"}, {"pin_number": "11, 12, 13, 14, 15", "pin_name": "PH", "pin_description": "开关相位节点。"}, {"pin_number": "16", "pin_name": "REFCAP", "pin_description": "需要一个 470nF 外部电容器用于内部基准。"}, {"pin_number": "17", "pin_name": "VSENSE", "pin_description": "gm 误差放大器的反相输入。"}, {"pin_number": "18", "pin_name": "COMP", "pin_description": "误差放大器输出和输出开关电流比较器的输入。将频率补偿连接到此引脚。"}, {"pin_number": "19", "pin_name": "SS/TR", "pin_description": "慢启动和跟踪。连接到此引脚的外部电容器可设置内部电压基准的上升时间。此引脚上的电压会覆盖内部基准。它可用于跟踪和定序。"}, {"pin_number": "20", "pin_name": "PWRGD", "pin_description": "电源正常故障引脚。如果由于热关断、压降、过压或 EN 关断或在慢启动期间输出电压较低，则断言为低电平。"}]}], "datasheet_cn": "是", "datasheet_en": "否", "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 5.5, "min_input_voltage": 3, "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": 3, "max_switch_frequency": 1, "quiescent_current": 2500, "high_side_mosfet_resistance": 45, "low_side_mosfet_resistance": 34, "over_current_protection_threshold": 6.2, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "OVP", "output_under_voltage_protection": "Power Good Indicator", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "Yes", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1.5, "output_reference_voltage": 0.807, "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "3V", "max_output_voltage": "可调", "min_output_voltage": "0.807V", "max_output_current": "3A", "max_switch_frequency": "1MHz", "quiescent_current": "2500μA", "high_side_mosfet_resistance": "45mΩ", "low_side_mosfet_resistance": "34mΩ", "over_current_protection_threshold": "6.2A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "无", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "自动重启", "output_short_circuit_protection": "自动重启", "over_temperature_protection": "自动重启", "hundred_percent_duty_cycle": "无", "output_discharge": "无", "integrated_ldo": "无", "frequency_synchronization": "Yes", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "无", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.807V", "loop_control_mode": "峰值电流模式"}}