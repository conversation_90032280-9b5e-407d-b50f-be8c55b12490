{"part_number": "TL5001A-Q1", "manufacturer": "Texas Instruments", "country": "USA", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "PWM控制器", "part_number_title": "PULSE-WIDTH-<PERSON><PERSON><PERSON><PERSON>TION CONTROL CIRCUITS", "features": ["Qualified for Automotive Applications", "Complete PWM Power Control", "3.6-V to 40-V Operation", "Internal Undervoltage-Lockout Circuit", "Internal Short-Circuit Protection", "Oscillator Frequency: 20 kHz to 500 kHz", "Variable Dead Time Provides Control Over Total Range", "±3% Tolerance on Reference Voltage", "Available in Q-Temperature Automotive", "High-Reliability Automotive Applications", "Configuration Control / Print Support", "Qualification to Automotive Standards"], "description": "The TL5001A incorporates on a single monolithic chip all the functions required for a pulse-width-modulation (PWM) control circuit. Designed primarily for power-supply control, the TL5001A contains an error amplifier, a regulator, an oscillator, a PWM comparator with a dead-time-control input, undervoltage lockout (UVLO), short-circuit protection (SCP), and an open-collector output transistor. The TL5001A has a typical reference voltage tolerance of ±3%. The error-amplifier common-mode voltage ranges from 0 V to 1.5 V. The noninverting input of the error amplifier is connected to a 1-V reference. Dead-time control (DTC) can be set to provide 0% to 100% dead time by connecting an external resistor between DTC and GND. The oscillator frequency is set by terminating RT with an external resistor to GND. During low Vcc conditions, the UVLO circuit turns the output off until Vcc recovers to its normal operating range. The TL5001A is characterized for operation from –40°C to 125°C.", "applications": ["Power-supply control", "High-Reliability Automotive Applications"], "ordering_information": [{"part_number": "TL5001A-Q1", "order_device": "TL5001AQDRG4Q1", "status": "Active", "package_type": "SOIC", "package_code": "D", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "package_drawing_code": "D0008A", "marking": "501AQ1", "pin_count": 8, "length": 5.0, "width": 3.98, "height": 1.75, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}, {"part_number": "TL5001A-Q1", "order_device": "TL5001AQDRQ1", "status": "Active", "package_type": "SOIC", "package_code": "D", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "package_drawing_code": "D0008A", "marking": "501AQ1", "pin_count": 8, "length": 5.0, "width": 3.98, "height": 1.75, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": "有", "pin_config": "有", "function_block_diagram": "有", "pin_function": [{"product_part_number": "TL5001A-Q1", "package_type": "D (SOIC)", "pins": [{"pin_number": "1", "pin_name": "OUT", "pin_description": "Open-collector output transistor for driving an external power switch."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Power supply input for the IC."}, {"pin_number": "3", "pin_name": "COMP", "pin_description": "Error amplifier output. Used for loop compensation."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Inverting input of the error amplifier. Used for feedback from the output voltage."}, {"pin_number": "5", "pin_name": "SCP", "pin_description": "Short-circuit protection timing capacitor connection."}, {"pin_number": "6", "pin_name": "DTC", "pin_description": "Dead-time control input. An external resistor to GND sets the dead time."}, {"pin_number": "7", "pin_name": "RT", "pin_description": "Oscillator timing resistor connection. An external resistor to GND sets the oscillator frequency."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Ground reference for the IC."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "TL5001A-Q1", "datasheet_path": "用户上传的PDF", "release_date": "2009-02", "version": "SLVS603B"}, "family_comparison": "未找到", "power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 40, "min_input_voltage": 3.6, "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "Controller", "max_switch_frequency": 500, "quiescent_current": 1400, "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "Timer based on COMP > 1.5V", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 3, "output_reference_voltage": 1, "loop_control_mode": "Voltage Mode", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "3.6V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "不适用(控制器)", "max_switch_frequency": "0.5MHz", "quiescent_current": "1400µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "外部可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±3%", "output_reference_voltage": "1V", "loop_control_mode": "电压模式"}}