{"part_number": "TPS50601A-SP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Military", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS50601A-SP 耐辐射 3V 至 7V 输入、6A 同步降压转换器", "features": ["5962-10221:", "耐辐射高达 100krad (Si) TID", "无低剂量率辐射损伤增强 (ELDRS) 100 krad (Si) - 10mrad(Si)/s", "单粒子锁定 (SEL) 对于 LET 的抗扰度 = 75MeV-cm²/mg", "SEB 和 SEGR 抗扰度可达到 75MeV-cm²/mg, 提供 SOA 曲线", "提供 SET/SEFI 横截面图", "峰值效率: 96.6% (Vo = 3.3V)", "集成了 58mΩ/50mΩ MOSFET", "电源轨: 3 至 7V (输入电压)", "6A 最大输出电流", "灵活的开关频率选项: 100kHz 至 1MHz 可调内部振荡器, 外部同步功能的频率范围: 100kHz 至 1MHz, 可针对主/从应用将同步引脚配置为 500kHz", "0.804V ±1.5% 的电压基准过热、辐射以及线路和负载调节", "单调启动至预偏置输出", "通过外部电容器进行可调节的软启动", "用于电源定序的输入使能和电源正常输出", "针对欠压和过压问题的电源正常输出监控", "可调节输入欠压锁定 (UVLO)", "20 引脚超小型耐热增强型陶瓷扁平封装 (HKH), 适用于太空应用"], "description": "TPS50601A-SP 是一款耐辐射的 7V、6A 同步降压转换器, 此转换器具有高效率并集成了高侧和低侧 MOSFET, 因此非常适合小型设计。通过电流模式控制减少组件数量, 并通过高开关频率缩小电感器封装尺寸, 从而进一步节省空间。此器件采用超小尺寸的热增强型 20 引脚陶瓷扁平封装。输出电压启动斜坡由 SS/TR 引脚控制, 可实现独立电源运行, 或者跟踪状态下的运行。此外, 正确配置启用与开漏电源正常引脚也可实现电源排序。此外, TPS50601A-SP 可配置为主/从模式, 从而提供高达 12A 的输出电流。高侧 FET 的逐周期电流限制可在过载情况下保护器件, 并通过低侧电源限流防止电流失控, 从而实现功能增强。此外, 还提供可关闭低侧 MOSFET 的低侧吸收电流限值, 以防止过多的反向电流。当芯片温度超过热关断温度时, 热关断禁用此器件。", "applications": ["用于 FPGA、微控制器、数据转换器和 ASIC 的太空卫星负载点电源", "航天卫星有效载荷", "耐辐射应用", "支持军用 (-55℃ 至 125℃) 温度范围", "可提供工程评估 (/EM) 样品"], "ordering_information": [{"part_number": "TPS50601A-SP", "order_device": "5962-1022102VSC", "status": "Active", "package_type": "CFP", "package_code": "HKH", "carrier_description": "TUBE", "carrier_quantity": 25, "package_drawing_code": "HKH0020A", "marking": "5962-1022102VS C, TPS50601AMHKHV", "pin_count": 20, "length": 12.95, "width": 7.57, "height": 2.416, "pitch": 1.27, "min_operation_temp": -55, "max_operation_temp": 125, "application_grade": "Military"}, {"part_number": "TPS50601A-SP", "order_device": "5962R1022102V9A", "status": "Active", "package_type": "KGD", "package_code": "XCEPT", "carrier_description": "OTHER", "carrier_quantity": 25, "pin_count": 0, "min_operation_temp": -55, "max_operation_temp": 125, "application_grade": "Military"}, {"part_number": "TPS50601A-SP", "order_device": "5962R1022102VSC", "status": "Active", "package_type": "CFP", "package_code": "HKH", "carrier_description": "TUBE", "carrier_quantity": 25, "package_drawing_code": "HKH0020A", "marking": "5962R1022102VS C, TPS50601AMHKHV", "pin_count": 20, "length": 12.95, "width": 7.57, "height": 2.416, "pitch": 1.27, "min_operation_temp": -55, "max_operation_temp": 125, "application_grade": "Military"}, {"part_number": "TPS50601A-SP", "order_device": "TPS50601AHKH/EM", "status": "Active", "package_type": "CFP", "package_code": "HKH", "carrier_description": "TUBE", "carrier_quantity": 25, "package_drawing_code": "HKH0020A", "marking": "TPS50601AHKH/EM EVAL ONLY", "pin_count": 20, "length": 12.95, "width": 7.57, "height": 2.416, "pitch": 1.27, "min_operation_temp": 25, "max_operation_temp": 25, "application_grade": "Other"}, {"part_number": "TPS50601A-SP", "order_device": "TPS50601AY/EM", "status": "Active", "package_type": "KGD", "package_code": "XCEPT", "carrier_description": "OTHER", "carrier_quantity": 5, "pin_count": 0, "min_operation_temp": 25, "max_operation_temp": 25, "application_grade": "Other"}], "typical_application_circuit": "有", "pin_config": "有", "function_block_diagram": "有", "pin_function": [{"product_part_number": "TPS50601A-SP", "package_type": "HKH (CFP-20)", "pins": [{"pin_number": "1", "pin_name": "GND", "pin_description": "控制电路的返回端。"}, {"pin_number": "2", "pin_name": "EN", "pin_description": "EN 引脚内部上拉，允许该引脚悬空以使能器件。使用两个电阻器调节输入欠压锁定 (UVLO)。"}, {"pin_number": "3", "pin_name": "RT", "pin_description": "在内部振荡模式下，在 RT 引脚和 GND 之间连接一个电阻器来设置开关频率。使该引脚悬空可将内部开关频率设置为 500 kHz。"}, {"pin_number": "4", "pin_name": "SYNC", "pin_description": "可选的 100kHz 至 1MHz 外部系统时钟输入。"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "开关稳压器控制电路的输入电源。"}, {"pin_number": "6, 7", "pin_name": "PVIN", "pin_description": "开关稳压器输出级的输入电源。"}, {"pin_number": "8, 9, 10", "pin_name": "PGND", "pin_description": "低侧功率 MOSFET 的返回端。"}, {"pin_number": "11, 12, 13, 14, 15", "pin_name": "PH", "pin_description": "开关相位节点。"}, {"pin_number": "16", "pin_name": "REFCAP", "pin_description": "内部基准所需的 470nF 外部电容器。"}, {"pin_number": "17", "pin_name": "VSENSE", "pin_description": "gm 误差放大器的反相输入。"}, {"pin_number": "18", "pin_name": "COMP", "pin_description": "误差放大器输出和输出开关电流比较器的输入。将频率补偿连接到此引脚。"}, {"pin_number": "19", "pin_name": "SS/TR", "pin_description": "慢启动和跟踪。连接到此引脚的外部电容器可设置内部电压基准上升时间。该引脚上的电压会覆盖内部基准。它可用于跟踪和定序。"}, {"pin_number": "20", "pin_name": "PWRGD", "pin_description": "电源正常故障引脚。如果输出电压因热关断、压降、过压或 EN 关断或在慢启动期间而过低，则断言为低电平。"}]}], "datasheet_cn": "有", "datasheet_en": "有", "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 7, "min_input_voltage": 3, "max_output_voltage": "未找到", "min_output_voltage": 0.804, "max_output_current": 6, "max_switch_frequency": 1, "quiescent_current": 5, "high_side_mosfet_resistance": 45, "low_side_mosfet_resistance": 34, "over_current_protection_threshold": 11, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "未找到", "integrated_ldo": "未找到", "frequency_synchronization": "Yes", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1.5, "output_reference_voltage": 0.804, "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "7V", "min_input_voltage": "3V", "max_output_voltage": "未找到", "min_output_voltage": "0.804V", "max_output_current": "6A", "max_switch_frequency": "1MHz", "quiescent_current": "5000µA", "high_side_mosfet_resistance": "45mΩ", "low_side_mosfet_resistance": "34mΩ", "over_current_protection_threshold": "11A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "是", "light_load_mode": "无", "power_good_indicator": "是", "soft_start": "外部可调", "input_over_voltage_protection": "无", "input_under_voltage_protection": "自动重启", "output_over_voltage_protection": "自动重启", "output_under_voltage_protection": "无", "output_over_load_protection": "电流限制", "output_short_circuit_protection": "自动重启", "over_temperature_protection": "自动重启", "hundred_percent_duty_cycle": "无", "output_discharge": "无", "integrated_ldo": "否", "frequency_synchronization": "是", "output_voltage_tracking": "是", "dynamic_voltage_setting": "否", "output_voltage_accuracy": "1.5%", "output_reference_voltage": "0.804V", "loop_control_mode": "峰值电流模式"}}