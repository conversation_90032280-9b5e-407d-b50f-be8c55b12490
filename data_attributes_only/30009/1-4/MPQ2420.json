{"part_number": "MPQ2420GF", "manufacturer": "Monolithic Power Systems, Inc.", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC转换器", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "MPQ2420-AEC1 75V, 0.3A, Synchronous Step-Down Converter with Watchdog AEC-Q100 Qualified", "features": ["20μA Quiescent Current for <PERSON> Only", "Wide 4.5V to 75V Operating Input Range (80V ABS MAX)", "1.2Ω/0.45Ω Internal Power MOSFETs", "Programmable Soft Start", "FB Tolerance: 1% at Room Temperature, 2% at Full Temperature", "Adjustable Output Voltage", "Integrated Window Watchdog", "Power-On Reset during Power-Up", "Under-Voltage Lockout and Thermal Shutdown", "Programmable Short Window Mode or Long Window Mode", "Low Shutdown Mode Current of 5μA", "TSSOP-16 EP Package", "Available in AEC-Q100 Grade 1"], "description": "The MPQ2420 is a step-down switching regulator with integrated high- and low-side, high-voltage power MOSFETs. It provides a highly efficient output of up to 0.3A. The integrated watchdog adds additional security redundancy to the system. The wide 4.5V to 75V input range accommodates a variety of step-down applications in an automotive environment. A 5μA shutdown mode quiescent current in a full temperature range is ideal for battery-powered applications. It allows for high-power conversion efficiency over a wide load range by scaling down the switching frequency under a light-load condition to reduce switching and gate driver losses. The start-up switching frequency and short circuit can also be scaled down to prevent inductor current runaway. Full protection features include under-voltage lockout (UVLO) and thermal shutdown. Thermal shutdown provides reliable, fault-tolerant operation. The MPQ2420 is available in a TSSOP-16 EP package.", "applications": ["Automotive Systems", "Industrial Power Systems", "Distributed Power Systems", "Battery-Powered Systems"], "ordering_information": [{"part_number": "MPQ2420GF", "order_device": "MPQ2420GF", "status": "Active", "package_type": "TSSOP-16 EP", "package_code": "TSSOP-16 EP", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "JEDEC MO-153, VARIATION ABT", "marking": "MPSYYWW MP2420 LLLLLL", "pin_count": "16", "length": "5.0", "width": "4.4", "height": "1.2", "pitch": "0.65", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MPQ2420", "package_type": "TSSOP-16 EP", "pins": [{"pin_number": "1", "pin_name": "WDO", "pin_description": "Watchdog output. WDO outputs the reset signal to MCU."}, {"pin_number": "2", "pin_name": "WDI", "pin_description": "Watchdog input. WDI receives the trigger signal from MCU."}, {"pin_number": "3", "pin_name": "MODE", "pin_description": "Mode switching. Pull MODE high to make the watchdog work in a long window mode. Pull MODE low to make the watchdog work in a short window mode. MODE has a weak internal pull-up."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. Connect GND as close as possible to the output capacitor to avoid high-current switch paths. Same as GND in Watchdog."}, {"pin_number": "5", "pin_name": "IN", "pin_description": "Input supply. IN requires a decoupling capacitor connected to ground to reduce switching spikes."}, {"pin_number": "6", "pin_name": "EN", "pin_description": "Enable input. Pull EN below the low threshold to shut the chip down. Pull EN above the high threshold to enable the chip. Float EN to disable the chip."}, {"pin_number": "7", "pin_name": "VREF", "pin_description": "Reference voltage output."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback input to the error amplifier. Connect FB to the tap of an external resistive divider between the output and GND. FB sets the regulation voltage when compared to the internal 1V reference."}, {"pin_number": "9", "pin_name": "SS", "pin_description": "Soft-start control input. Connect a capacitor from SS to GND to set the soft-start period."}, {"pin_number": "10", "pin_name": "POK", "pin_description": "Open-drain power-good output. A high output indicates that VOUT is higher than 90% of the reference. POK is pulled down during shutdown."}, {"pin_number": "11", "pin_name": "BIAS", "pin_description": "Controller bias input. BIAS supplies current to the internal circuit when VBIAS > 2.9V and provides a feedback input for the SOIC8E package, which has a fixed output only."}, {"pin_number": "12", "pin_name": "BST", "pin_description": "Bootstrap. BST provides a positive power supply for the internal floating high-side MOSFET driver. Connect a bypass capacitor between BST and SW."}, {"pin_number": "13", "pin_name": "SW", "pin_description": "Switch node."}, {"pin_number": "14", "pin_name": "VCC", "pin_description": "Power input."}, {"pin_number": "15", "pin_name": "TIMER", "pin_description": "Watchdog timer. Set the time out with an external resistor"}, {"pin_number": "16", "pin_name": "/WD_DIS", "pin_description": "Watchdog disable. Pull /WD_DIS low to disable the watchdog. Pull /WD_DIS high to enable the watchdog. /WD_DIS has a weak internal pull-up."}, {"pin_number": "Exposed Pad", "pin_name": "Exposed Pad", "pin_description": "Connect exposed pad to GND plane for optimal thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MPQ2420-AEC1", "datasheet_path": "用户上传", "release_date": "2016-05-24", "version": "1.0"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "75", "min_input_voltage": "4.5", "max_output_voltage": "67.5", "min_output_voltage": "1", "max_output_current": "0.3", "max_switch_frequency": "未找到", "quiescent_current": "20", "high_side_mosfet_resistance": "1200", "low_side_mosfet_resistance": "450", "over_current_protection_threshold": "0.72", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM/Frequency Foldback", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "OVP", "output_under_voltage_protection": "未找到", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "2", "output_reference_voltage": "1", "loop_control_mode": "Peak Current Mode", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "75V", "min_input_voltage": "4.5V", "max_output_voltage": "0.9*VIN", "min_output_voltage": "1V", "max_output_current": "0.3A", "max_switch_frequency": "未找到", "quiescent_current": "20μA", "high_side_mosfet_resistance": "1200mΩ", "low_side_mosfet_resistance": "450mΩ", "over_current_protection_threshold": "0.72A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM/Frequency Foldback", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}}