{"part_number": "MP8774H", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "开关稳压器", "category_lv3": "降压型开关稳压器", "part_number_title": "12A, Wide-Input 3V to 18V, 1.4MHz Synchronous Step-Down Converter with PG and External Soft Start in 3mmx3mm QFN Package", "features": ["Output Adjustable from 0.6V", "Wide 3V to 18V Operating Input Range", "12A Output Current", "16mΩ/5.5mΩ Low RDS(ON) Internal Power MOSFETS", "100μA Quiescent Current", "High-Efficiency Synchronous Mode Operation", "Pre-Biased Start-Up", "Fixed 1.4MHz Switching Frequency", "External Programmable Soft-Start Time", "Enable (EN) and Power Good (PG) for Power Sequencing", "Over-Current Protection and Hiccup Mode", "Thermal Shutdown", "Available in a QFN-16 (3mmx3mm) Package"], "description": "The MP8774H is a fully integrated, high-frequency, synchronous, rectified, step-down, switch-mode converter with internal power MOSFETs. The MP8774H offers a very compact solution that achieves 12A of continuous output current with excellent load and line regulation over a wide input range. The MP8774H uses synchronous mode operation for higher efficiency over the output current load range. Constant-on-time (COT) control operation provides very fast transient response, easy loop design, and very tight output regulation. Full protection features include short-circuit protection (SCP), over-current protection (OCP), under-voltage protection (UVP), and thermal shutdown. The MP8774H requires a minimal number of readily available, standard external components, and is available in a space-saving QFN-16 (3mmx3mm) package.", "applications": ["Security Cameras", "AP Routers, xDSL Devices", "Digital Set-Top Boxes", "Flat-Panel Television and Monitors", "General Purpose"], "ordering_information": [{"part_number": "MP8774H", "order_device": "MP8774HGQ", "status": "未找到", "package_type": "QFN-16 (3mmx3mm)", "package_code": "QFN-16", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "BJUY", "pin_count": "16", "length": "3", "width": "3", "height": "1.0", "pitch": "0.55", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Industrial"}, {"part_number": "MP8774H", "order_device": "MP8774HGQ-Z", "status": "未找到", "package_type": "QFN-16 (3mmx3mm)", "package_code": "QFN-16", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "BJUY", "pin_count": "16", "length": "3", "width": "3", "height": "1.0", "pitch": "0.55", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "MP8774H", "package_type": "QFN-16 (3mmx3mm)", "pins": [{"pin_number": "1, 15", "pin_name": "NC", "pin_description": "No connection. NC must be left floating."}, {"pin_number": "2", "pin_name": "BST", "pin_description": "Bootstrap. Connect a capacitor between SW and BST to form a floating supply across the high-side switch driver. A BST resistor less than 4.7Ω is recommended."}, {"pin_number": "3", "pin_name": "EN", "pin_description": "Enable. Pull EN high to enable the MP8774H. When floating, EN is pulled down to GND and disabled by an internal 3.3MΩ resistor."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Feedback. FB sets the output voltage when connected to the tap of an external resistor divider between output and GND."}, {"pin_number": "5", "pin_name": "AGND", "pin_description": "Signal ground. AGND is not connected to the system ground internally. Ensure that AGND is connected to the system ground in the PCB layout."}, {"pin_number": "6", "pin_name": "SS", "pin_description": "Soft start. Connect a capacitor across SS and GND to set the soft-start time and avoid inrush current at start-up."}, {"pin_number": "7", "pin_name": "PG", "pin_description": "Power good output. The output of PG is an open drain. PG changes state if UVP, OCP, OTP, or OV occurs."}, {"pin_number": "8", "pin_name": "VIN", "pin_description": "Supply voltage. The MP8774H operates from a 3V to 18V input rail. A capacitor (C1) is needed to decouple the input rail. Use a wide PCB trace to make the connection."}, {"pin_number": "9, 10, 11, 12, 13", "pin_name": "PGND", "pin_description": "System ground. PGND is the reference ground of the regulated output voltage. PGND requires careful consideration during the PCB layout. PGND is recommended to be connected to GND with coppers and vias."}, {"pin_number": "14", "pin_name": "VCC", "pin_description": "Internal bias supply output. Decouple VCC with a 1μF capacitor. Place the VCC capacitor close to VCC and GND."}, {"pin_number": "16", "pin_name": "SW", "pin_description": "Switch output. Connect SW with a wide PCB trace."}]}], "datasheet_en": {"name": "MP8774H Rev. 1.0", "path": "MP8774H.pdf", "release_date": "2019-05-22", "version": "1.0"}, "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "18", "min_input_voltage": "3", "max_output_voltage": "12", "min_output_voltage": "0.6", "max_output_current": "12", "max_switch_frequency": "1.6", "quiescent_current": "100", "high_side_mosfet_resistance": "16", "low_side_mosfet_resistance": "5.5", "over_current_protection_threshold": "14", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1.5", "output_reference_voltage": "0.6", "loop_control_mode": "固定导通时间控制", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "3V", "max_output_voltage": "12V", "min_output_voltage": "0.6V", "max_output_current": "12A", "max_switch_frequency": "1.6MHz", "quiescent_current": "100µA", "high_side_mosfet_resistance": "16mΩ", "low_side_mosfet_resistance": "5.5mΩ", "over_current_protection_threshold": "14A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "0.6V", "loop_control_mode": "固定导通时间控制"}}