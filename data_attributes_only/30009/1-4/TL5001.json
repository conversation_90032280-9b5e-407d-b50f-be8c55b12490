{"part_number": "TL5001", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": ["Industrial", "Automotive", "Military"], "category_lv1": "电源管理芯片", "category_lv2": "PWM控制器", "category_lv3": "电压模式PWM控制器", "part_number_title": "TL5001, TL5001A PULSE-WIDTH-MODULATION CONTROL CIRCUITS", "features": ["Complete PWM Power Control", "3.6-V to 40-V Operation", "Internal Undervoltage-Lockout Circuit", "Internal Short-Circuit Protection", "Oscillator Frequency . . . 20 kHz to 500 kHz", "Variable Dead Time Provides Control Over Total Range", "±3% Tolerance on Reference Voltage (TL5001A)", "Available in Q-Temp Automotive HighRel Automotive Applications Configuration Control / Print Support Qualification to Automotive Standards"], "description": "The TL5001 and TL5001A incorporate on a single monolithic chip all the functions required for a pulse-width-modulation (PWM) control circuit. Designed primarily for power-supply control, the TL5001/A contains an error amplifier, a regulator, an oscillator, a PWM comparator with a dead-time-control input, undervoltage lockout (UVLO), short-circuit protection (SCP), and an open-collector output transistor. The TL5001A has a typical reference voltage tolerance of ±3% compared to ±5% for the TL5001.", "applications": [], "ordering_information": [{"part_number": "TL5001", "order_device": "TL5001CD", "status": "Active", "package_type": "SOIC", "package_code": "D", "carrier_description": "TUBE", "carrier_quantity": 75, "package_drawing_code": "D", "marking": "5001C", "pin_count": 8, "length": 8.75, "width": 6.2, "height": 1.75, "pitch": 1.27, "min_operation_temp": -20, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "Industrial"}, {"part_number": "TL5001", "order_device": "TL5001CP", "status": "Active", "package_type": "PDIP", "package_code": "P", "carrier_description": "TUBE", "carrier_quantity": 50, "package_drawing_code": "P", "marking": "TL5001CP", "pin_count": 8, "length": 10.16, "width": 6.6, "height": 5.08, "pitch": 2.54, "min_operation_temp": -20, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "Industrial"}, {"part_number": "TL5001", "order_device": "TL5001ID", "status": "Active", "package_type": "SOIC", "package_code": "D", "carrier_description": "TUBE", "carrier_quantity": 75, "package_drawing_code": "D", "marking": "5001I", "pin_count": 8, "length": 8.75, "width": 6.2, "height": 1.75, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "Industrial"}, {"part_number": "TL5001", "order_device": "TL5001QD", "status": "Active", "package_type": "SOIC", "package_code": "D", "carrier_description": "TUBE", "carrier_quantity": 75, "package_drawing_code": "D", "marking": "5001Q", "pin_count": 8, "length": 8.75, "width": 6.2, "height": 1.75, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "未找到", "application_grade": "Automotive"}, {"part_number": "TL5001", "order_device": "TL5001MJG", "status": "Active", "package_type": "CDIP", "package_code": "JG", "carrier_description": "TUBE", "carrier_quantity": 50, "package_drawing_code": "JG0008A", "marking": "TL5001MJG", "pin_count": 8, "length": 10.16, "width": 7.11, "height": 5.08, "pitch": 2.54, "min_operation_temp": -55, "max_operation_temp": 125, "output_voltage": "未找到", "application_grade": "Military"}, {"part_number": "TL5001", "order_device": "TL5001MFK", "status": "Active", "package_type": "LCCC", "package_code": "FK", "carrier_description": "TUBE", "carrier_quantity": 55, "package_drawing_code": "FK", "marking": "TL5001MFK", "pin_count": 20, "length": 8.89, "width": 8.89, "height": 2.03, "pitch": 1.27, "min_operation_temp": -55, "max_operation_temp": 125, "output_voltage": "未找到", "application_grade": "Military"}], "typical_application_circuit": "是", "pin_config": "是", "function_block_diagram": "是", "pin_function": [{"product_part_number": "TL5001", "package_type": "D/JG/P", "pins": [{"pin_number": "1", "pin_name": "OUT", "pin_description": "Open-collector output transistor for driving an external power switch."}, {"pin_number": "2", "pin_name": "VCC", "pin_description": "Power supply input for the IC."}, {"pin_number": "3", "pin_name": "COMP", "pin_description": "Output of the error amplifier, used for loop compensation."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Inverting input of the error amplifier, connected to the converter's output via a resistive divider."}, {"pin_number": "5", "pin_name": "SCP", "pin_description": "Short-circuit protection timing input. An external capacitor sets the time-out period."}, {"pin_number": "6", "pin_name": "DTC", "pin_description": "Dead-time control input. An external resistor sets the maximum duty cycle."}, {"pin_number": "7", "pin_name": "RT", "pin_description": "Oscillator timing input. An external resistor sets the switching frequency."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Ground reference for the IC."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "TL5001, TL5001A PULSE-WIDTH-MODULATION CONTROL CIRCUITS", "path": "用户上传文件", "version": "SLVS084F", "release_date": "2002-01"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "40V", "min_input_voltage": "3.6V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "外置(控制器)", "max_switch_frequency": "500kHz", "quiescent_current": "1.4mA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "可调", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±5%", "output_reference_voltage": "1V", "loop_control_mode": "电压模式"}}