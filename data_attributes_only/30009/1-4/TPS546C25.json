{"part_number": "TPS546C25", "manufacturer": "Texas Instruments", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS546C25 4V to 18V Input, 35A, 4 × Stackable, Synchronous Buck Converter With PMBus® and Telemetry", "features": ["4V to 18V input voltage without external bias", "2.7V to 18V input voltage with external bias", "0.4V to 5.5V output voltage", "Supports 35A single-phase or 2 ×, 3 ×, or 4 × stacked configurations", "Rdson_HS = 3.3mΩ, Rdson_LS = 1mΩ", "400kHz to 2MHz operating frequency (four discrete settings through pinstrap, additional settings through PMBus)", "PMBus® programmability: Revision 1.5 Compliant with PASSKEY security feature; Input voltage, output voltage, output current, temperature telemetry; Programmable overcurrent, overvoltage, undervoltage, overtemperature protections; Includes single command write function in stacked configuration; Extended write protection feature; Non-volatile memory to store configuration settings", "Two methods for programming the output voltage: Internal resistor divider (discrete settings), with boot-up voltage selected by pin strapping; External resistor divider (continuous settings), with boot-up voltage selected by VBOOT field", "Precision voltage reference and differential remote sense for high output accuracy: +/– 0.5% DAC accuracy from 0°C to 85°C junction; +/– 1% VOUT tolerance from –40°C to 125°C junction", "Selectable FCCM/DCM in single phase only", "Start-up without PMBus communication through pin strapping", "Safe start-up into prebiased output", "Programmable soft-start time from 0.5ms to 16ms", "Programmable soft-stop time from 0.5ms to 4ms", "D-CAP4 control topology with fast transient response, supporting all ceramic output capacitors", "Programmable internal loop compensation", "Selectable cycle-by-cycle valley current limit", "Open-drain power-good output"], "description": "The TPS546C25 device is a highly integrated, buck converter with D-CAP4 control topology for fast transient response. All programmable parameters can be configured by the PMBus interface and stored in NVM as the new default values to minimize the external component count. Pinstrap options allow for configuration as primary or secondary, stack position and stack number, DCM (single phase only) or FCCM, overcurrent limit, fault response, internal or external feedback resistor, output voltage selection or range, switching frequency, and compensation. The PMBus interface with 1MHz clock support gives a convenient, standardized digital interface for configuration as well as telemetry of key parameters including output voltage, output current, and internal die temperature. Response to fault conditions can be set to restart, latch off, or ignore, depending on system requirements. Two, three, and four TPS546C25 devices can be interconnected to provide up to 140A on a single output. The device has an option to overdrive the internal 5V LDO with an external 5V supply through the VDRV and VCC pins to improve efficiency, reduce power dissipation, and enable start-up with a lower input voltage. The TPS546C25 is a lead-free device and is RoHS compliant without exemption.", "applications": ["Server and cloud-computing POLs", "Hardware accelerator", "Network interface card"], "ordering_information": [{"part_number": "TPS546C25", "order_device": "PTPS546C25VBDR", "status": "Active", "package_type": "WQFN-FCRLF", "package_code": "VBD", "carrier_description": "LARGE T&R", "carrier_quantity": 3000, "package_drawing_code": "FCRLF", "marking": "未找到", "pin_count": 33, "length": 5, "width": 4, "height": 0.7, "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C25", "order_device": "PTPS546C25VBDR.A", "status": "Active", "package_type": "WQFN-FCRLF", "package_code": "VBD", "carrier_description": "LARGE T&R", "carrier_quantity": 3000, "package_drawing_code": "FCRLF", "marking": "未找到", "pin_count": 33, "length": 5, "width": 4, "height": 0.7, "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C25", "order_device": "PTPS546C25VBDR.B", "status": "Active", "package_type": "WQFN-FCRLF", "package_code": "VBD", "carrier_description": "LARGE T&R", "carrier_quantity": 3000, "package_drawing_code": "FCRLF", "marking": "未找到", "pin_count": 33, "length": 5, "width": 4, "height": 0.7, "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "未找到", "application_grade": "未找到"}], "typical_application_circuit": "Page 2, Simplified Schematic; Page 142, Figure 8-1. 1.2V, 35A Output Application", "pin_config": "Page 4, Figure 4-1. VBD 33-pin WQFN Package (Top View)", "function_block_diagram": "Page 14, Figure 6-1. Block Diagram when Internal Feedback is Selected", "pin_function": [{"product_part_number": "TPS546C25", "package_type": "VBD (WQFN-FCRLF, 33)", "pins": [{"pin_number": "26, 31", "pin_name": "AGND", "pin_description": "Ground pin, reference point for internal control circuitry."}, {"pin_number": "20", "pin_name": "BOOT", "pin_description": "Supply rail for the high-side gate driver (boost terminal). Connect the bootstrap capacitor from this pin to PHASE pin. A high temperature (X7R) 0.1μF or greater value ceramic capacitor is recommended."}, {"pin_number": "21", "pin_name": "CNTL", "pin_description": "CTRL pin, an active-high input pin that, when asserted high, causes the converter to begin the soft-start sequence for the output voltage rail."}, {"pin_number": "25", "pin_name": "GOSNS", "pin_description": "Negative input of the differential remote sense circuit, connect to the ground sense point on the load side."}, {"pin_number": "1", "pin_name": "ISHARE", "pin_description": "ISHARE pin for stackable configuration. Tie this pin to other ISHARE pins in the stack. Do not connect (float) in standalone configuration."}, {"pin_number": "30", "pin_name": "MSEL1", "pin_description": "Use a resistor to AGND to select options for the device. See Pin Strapping."}, {"pin_number": "4", "pin_name": "MSEL2", "pin_description": "Use a resistor to AGND to select configuration options for the device. See Pin Strapping."}, {"pin_number": "2", "pin_name": "PG", "pin_description": "Open-drain power-good indicator."}, {"pin_number": "8, 9, 16, 17, 33", "pin_name": "PGND", "pin_description": "Power ground for the internal power stage."}, {"pin_number": "19", "pin_name": "PHASE", "pin_description": "Return for high-side MOSFET driver. Shorted to SW internally. Connect the bootstrap capacitor from BOOT pin to PHASE pin."}, {"pin_number": "23", "pin_name": "PMB_ADDR/VORST#", "pin_description": "The PMBus address, Primary or Secondary, Internal or External Feedback, Over-current Limit, Soft-start, and Fault Response can be set by tying an external resistor between this pin and AGND. See Pin Strapping."}, {"pin_number": "27", "pin_name": "PMB_CLK", "pin_description": "PMBus clock pin, open drain."}, {"pin_number": "28", "pin_name": "PMB_DATA", "pin_description": "PMBus bi-directional data pin, open drain."}, {"pin_number": "7, 18, 32", "pin_name": "PVIN", "pin_description": "Power input for both the power stage and the input of the internal VCC LDO."}, {"pin_number": "29", "pin_name": "SMB_ALERT_#", "pin_description": "SMBALERT# as described in the SMBus specification. The pin is open-drain. The SMBALERT# indicator is used in conjunction with the Alert Response Address (ARA). During nominal operation, the SMBALERT# is held high."}, {"pin_number": "10–15", "pin_name": "SW", "pin_description": "Output switching terminal of the power converter. Connect these pins to the output inductor."}, {"pin_number": "3", "pin_name": "TRIGGER", "pin_description": "TRIGGER pin for stackable configuration. Tie this pin to other TRIGGER pins in the stack. Do not connect (float) in standalone configuration."}, {"pin_number": "5", "pin_name": "VCC", "pin_description": "Supply for analog control circuitry. Connect a 1Ohm resistor from VDRV to this pin and bypass with a 2.2μF capacitor to AGND. Check layout guidelines for more details."}, {"pin_number": "6", "pin_name": "VDRV", "pin_description": "Internal 5V regulator output and internal connection to the gate drivers. An external 5V bias can be connected to this pin to save the power losses on the internal LDO. A 2.2μF (or 4.7μF), at least 6.3V rating ceramic capacitor is required to be placed from VDRV pin to PGND pins to decouple the noise generated by driver circuitry. Check layout guidelines for more details."}, {"pin_number": "24", "pin_name": "VOSNS", "pin_description": "This pin is VOSNS and is the positive input of the differential remote sense circuit, connect to the Vout sense point on the load side."}, {"pin_number": "22", "pin_name": "VSEL/FB", "pin_description": "When the device is configured to use the internal FB divider, this pin is VSEL. Use a resistor to AGND to select the output voltage. See Table TBD. When the device is configured for an external resister divider, this pin is the feedback pin of the device. Connect this pin to the midpoint of a resistor divider to set the output voltage."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "TPS546C25", "datasheet_path": "用户上传", "release_date": "JULY 2024", "version": "SLVSH95"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 18, "min_input_voltage": 4, "max_output_voltage": 5.5, "min_output_voltage": 0.4, "max_output_current": 35, "max_switch_frequency": 2, "quiescent_current": 10, "high_side_mosfet_resistance": 3.3, "low_side_mosfet_resistance": 1, "over_current_protection_threshold": "Programmable", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "DCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch/Hiccup", "output_under_voltage_protection": "Latch/Hiccup", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Current Limit", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy_percentage": 0.5, "output_reference_voltage_value": 0.4, "loop_control_mode": "D-CAP4", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "4V", "max_output_voltage": "5.5V", "min_output_voltage": "0.4V", "max_output_current": "35A", "max_switch_frequency": "2MHz", "quiescent_current": "10mA", "high_side_mosfet_resistance": "3.3mΩ", "low_side_mosfet_resistance": "1mΩ", "over_current_protection_threshold": "10-40A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "DCM, FCCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "Latch", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.4V", "loop_control_mode": "固定导通时间控制"}}