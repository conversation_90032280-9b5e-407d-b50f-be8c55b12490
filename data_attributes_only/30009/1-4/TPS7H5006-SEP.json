{"part_number": "TPS7H5005-SEP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "航天级", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC控制器", "category_lv3": "PWM控制器", "part_number_title": "采用增强型航天塑料的TPS7H500x-SEP 抗辐射 2MHz 电流模式 PWM 控制器", "features": ["耐辐射: SEL、SEB 和 SEGR 对于 LET 的抗扰度 = 43MeV-cm²/mg; SET 和 SEFI的 LET 特征值高达 43MeV-cm²/mg; 每个晶圆批次的保障 TID 高达 50krad(Si)", "输入电压: 4V至 14V", "在温度、辐射以及线路和负载调节范围内提供 0.613V +0.7%/-1%的电压基准", "开关频率范围为100kHz至2MHz", "外部时钟同步功能", "同步整流输出 (TPS7H5005-SEP、TPS7H5006-SEP、TPS7H5007-SEP)", "可调死区时间 (TPS7H5005-SEP、TPS7H5006-SEP)", "可调前沿消隐时间 (TPS7H5005-SEP、TPS7H5006-SEP、TPS7H5008-SEP)", "可配置的占空比限值 (TPS7H5005-SEP、TPS7H5006-SEP、TPS7H5007-SEP)", "可调斜坡补偿和软启动", "24 引脚 TSSOP 封装", "增强型航天塑料: 受控基线, Au 键合线和 NiPdAu 铅涂层, 符合 NASA ASTM E595 释气规格要求, 制造、组装和测试一体化基地, 延长了产品生命周期, 延长了产品变更通知周期, 产品可追溯性"], "description": "TPS7H500x SEP (由 TPS7H5005-SEP、TPS7H5006-SEP、TPS7H5007-SEP 和 TPS7H5008-SEP 组成)是采用航天增强型塑料制成的高速、抗辐射 PWM 控制器系列。这些控制器提供的许多功能有助于设计面向太空应用的直流/直流转换器拓扑。控制器具有 0.613V +0.7%/-1% 的内部精密基准,可配置开关频率高达 2MHz。每个器件都提供可编程斜坡补偿和软启动功能。TPS7H500x-SEP 系列可通过 SYNC 引脚使用外部时钟来驱动,也可使用内部振荡器以用户编程的频率来驱动。此控制器系列为用户提供了各种选项,这些选项用于切换输出、同步整流功能、死区时间(固定或可配置)、前沿消隐时间(固定或可配置)和占空比限制。TPS7H500x-SEP 系列中的每个器件都采用 24 引脚 TSSOP 封装。", "applications": ["用于FPGA、微控制器、数据转换器和ASIC 的太空卫星负载点电源", "通信负载", "命令和数据处理", "光学成像有效载荷", "雷达成像有效载荷", "卫星电力系统"], "ordering_information": [{"part_number": "TPS7H5005-SEP", "order_device": "TPS7H5005MPWTSEP", "status": "Active", "package_type": "TSSOP", "package_code": "PW", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "PW0024A", "marking": "7H5005PW", "pin_count": "24", "length": "7.8", "width": "4.4", "height": "1.2", "pitch": "0.65", "min_operation_temp": "-55", "max_operation_temp": "125", "output_voltage": "不适用", "application_grade": "航天级"}], "typical_application_circuit": "Page 49, 图 9-1. Typical Application Schematic", "pin_config": "Page 4, 图 6-1. TPS7H5005-SEP PW Package 24-Pin TSSOP (Top View)", "function_block_diagram": "Page 26, 图 8-1. TPS7H5005-SEP Functional Block Diagram", "pin_function": [{"product_part_number": "TPS7H5005-SEP", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "在内部振荡模式下，RT引脚必须使用一个到AVSS的电阻。当RT引脚悬空时，SYNC引脚需要一个200kHz至4MHz的外部时钟。外部时钟的频率必须是所需开关频率的两倍。"}, {"pin_number": "2", "pin_name": "PS", "pin_description": "原边关断到同步整流器导通的死区时间设置。可通过外部电阻连接到AVSS进行编程。"}, {"pin_number": "3", "pin_name": "SP", "pin_description": "同步整流器关断到原边导通的死区时间设置。可通过外部电阻连接到AVSS进行编程。"}, {"pin_number": "4", "pin_name": "LEB", "pin_description": "前沿消隐时间设置。可通过外部电阻连接到AVSS进行编程。"}, {"pin_number": "5", "pin_name": "HICC", "pin_description": "逐周期电流限制时间延迟和打嗝时间设置。延迟时间和打嗝时间由从HICC到AVSS的电容器决定。将此引脚连接到AVSS可禁用打嗝模式。"}, {"pin_number": "6", "pin_name": "SYNC", "pin_description": "当RT引脚悬空时，SYNC被配置为200kHz至4MHz外部时钟的输入。在这种情况下，外部时钟输入被反相，系统时钟将以外部时钟输入频率的一半运行。当RT引脚连接电阻到AVSS时，SYNC输出一个200kHz至4MHz的时钟信号，频率是器件开关频率的两倍，与器件开关同相。"}, {"pin_number": "7", "pin_name": "DCL", "pin_description": "占空比限制可配置性。对于TPS7H5005-SEP，连接到AVSS为50%占空比限制，悬空为75%，连接到VLDO为100%。"}, {"pin_number": "8", "pin_name": "EN", "pin_description": "将EN引脚连接到VLDO引脚或大于0.6V的外部源可使能器件。此外，输入欠压锁定（UVLO）可通过两个电阻进行调整。"}, {"pin_number": "9", "pin_name": "VIN", "pin_description": "器件的输入电源。输入电压范围为4V至14V。"}, {"pin_number": "10", "pin_name": "OUTA", "pin_description": "原边开关输出A。"}, {"pin_number": "11", "pin_name": "OUTB", "pin_description": "原边开关输出B。仅当DCL = AVSS时有效。"}, {"pin_number": "12, 13", "pin_name": "NC", "pin_description": "无连接。如果需要，可以连接到AVSS以避免浮空金属。"}, {"pin_number": "14", "pin_name": "SRB", "pin_description": "同步整流器输出B。仅当DCL = AVSS时有效。"}, {"pin_number": "15", "pin_name": "SRA", "pin_description": "同步整流器输出A。"}, {"pin_number": "16", "pin_name": "AVSS", "pin_description": "器件的地。"}, {"pin_number": "17", "pin_name": "VLDO", "pin_description": "内部稳压器输出。需要至少一个1μF的外部电容器到AVSS。"}, {"pin_number": "18", "pin_name": "CS_ILIM", "pin_description": "用于PWM控制和逐周期过流保护的电流检测。CS_ILIM上超过1.05V的输入电压将触发PWM控制器中的过流。与PWM比较器输入的COMP/2电压相比，CS_ILIM上的感测波形包含150mV的偏移。"}, {"pin_number": "19", "pin_name": "FAULT", "pin_description": "故障保护引脚。当FAULT引脚的上升阈值被超过时，输出将停止开关。在外部电压降至下降阈值以下后，器件将在设定的延迟后重新启动。将此引脚连接到AVSS以禁用FAULT。"}, {"pin_number": "20", "pin_name": "REFCAP", "pin_description": "1.2V内部参考。需要一个470nF的外部电容器到AVSS。"}, {"pin_number": "21", "pin_name": "RSC", "pin_description": "从RSC到AVSS的电阻器设置所需的斜坡补偿。"}, {"pin_number": "22", "pin_name": "SS", "pin_description": "软启动。连接到此引脚的外部电容器设置内部电压参考的上升时间。此引脚上的电压会覆盖内部参考。它可用于跟踪和排序。"}, {"pin_number": "23", "pin_name": "VSENSE", "pin_description": "误差放大器的反相输入。"}, {"pin_number": "24", "pin_name": "COMP", "pin_description": "误差放大器输出。将频率补偿连接到此引脚。"}]}], "datasheet_cn": {"datasheet_name": "ZHCSOF4A", "datasheet_path": "ZHCSOF4A FEBRUARY 2022 REVISED SEPTEMBER 2022", "release_date": "2022-09", "version": "A"}, "datasheet_en": {"datasheet_name": "SLVSGG1", "datasheet_path": "未找到", "release_date": "未找到", "version": "未找到"}, "family_comparison": "Page 3, 表 5-1. TPS7H500x-SEP Device Comparison Table", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET, 氮化镓(GAN)", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "14V", "min_input_voltage": "4V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "可调", "max_switch_frequency": "2MHz", "quiescent_current": "3000µA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "脉冲跳跃 (Pulse Skipping)", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1%", "output_reference_voltage": "0.613V", "loop_control_mode": "峰值电流模式"}}