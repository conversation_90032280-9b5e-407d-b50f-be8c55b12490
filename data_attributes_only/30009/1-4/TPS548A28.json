{"part_number": "TPS548A28", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压芯片(<PERSON>)", "part_number_title": "具有遥感、3V 内部LDO 和断续电流限制功能的 TPS548A28 2.7V至16V 输入、15A 同步降压转换器", "features": ["输入范围为4V至16V,电流高达15A,无外部偏压", "输入范围为3V 至16V,电流高达12A,无外部偏压", "输入范围为2.7V至16V时,电流高达15A,外部偏压范围为3.13V至5.3V", "输出电压范围: 0.6V至5.5V", "集成式 10.2mΩ 和 3.1mΩ MOSFET", "D-CAP3™, 可提供超快负载阶跃响应", "支持所有陶瓷输出电容器", "在-40℃至+125℃ 结温下实现差分遥感,VREF为0.6V ±1%", "自动跳跃 Eco-mode™ 可实现高轻负载效率", "通过RTRIP 实现可编程电流限制", "引脚可选开关频率: 600kHz、800kHz、1MHz", "可编程软启动时间", "外部基准输入,用于跟踪", "预偏置启动功能", "开漏电源正常状态输出", "在发生 OC 和UV 故障时进入断续模式,在发生OV 故障时进入闭锁模式", "4mm x 3mm 21 引脚 QFN 封装", "引脚与 12A TPS54JA20 兼容", "完全符合RoHS标准,无需豁免"], "description": "TPS548A28 器件是一款具有自适应导通时间 D-CAP3 控制模式的高效率、小尺寸同步降压转换器。该器件不需要外部补偿,因此易于使用并且仅需要很少的外部元件。该器件非常适合空间受限的数据中心应用。TPS548A28 器件具有差分遥感功能和高性能集成 MOSFET,在整个工作结温范围具有高精度(±1%) 0.6V 电压基准。该器件具有快速负载瞬态响应、精确负载调节和线路调节、跳跃模式或 FCCM 运行以及可编程软启动功能。TPS548A28 是一款无铅器件,完全符合RoHS标准,无需豁免。", "applications": ["机架式服务器和刀片式服务器", "硬件加速器和插件卡", "数据中心交换机", "工业PC"], "ordering_information": [{"part_number": "TPS548A28", "order_device": "TPS548A28RWWR", "status": "Active", "package_type": "VQFN-HR", "package_code": "RWW", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RWW0021A", "marking": "T548A28", "pin_count": "21", "length": "4.1", "width": "3.1", "height": "1", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS548A28", "order_device": "TPS548A28RWWR.A", "status": "Active", "package_type": "VQFN-HR", "package_code": "RWW", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RWW0021A", "marking": "T548A28", "pin_count": "21", "length": "4.1", "width": "3.1", "height": "1", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS548A28", "order_device": "TPS548A28RWWR.B", "status": "Active", "package_type": "VQFN-HR", "package_code": "RWW", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RWW0021A", "marking": "T548A28", "pin_count": "21", "length": "4.1", "width": "3.1", "height": "1", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}], "typical_application_circuit": "Page 1, 简化版原理图", "pin_config": "Page 3, 图 5-1. RWW 封装 21 引脚 VQFN-HR 顶视图", "function_block_diagram": "Page 12, 图 7-2. 功能方框图", "pin_function": [{"product_part_number": "TPS548A28", "package_type": "VQFN-HR", "pins": [{"pin_number": "1", "pin_name": "BOOT", "pin_description": "高侧栅极驱动器(升压端子)的电源轨。从该引脚到 SW 节点之间连接自举电容器。"}, {"pin_number": "2", "pin_name": "AGND", "pin_description": "接地引脚,内部控制电路的基准点"}, {"pin_number": "3", "pin_name": "TRIP", "pin_description": "电流限制设置引脚。将一个电阻连接到AGND即可设置电流限制跳变点。强烈建议使用容差为±1%的电阻。有关OCL设置的详细信息,请参阅节7.3.9。"}, {"pin_number": "4", "pin_name": "MODE", "pin_description": "MODE 引脚可设置强制连续导通模式 (FCCM) 或跳跃模式的工作模式。它还通过在MODE 引脚和AGND 引脚之间连接一个电阻来选择工作频率。建议使用容差为±1%的电阻。详细信息,请参阅表7-1。"}, {"pin_number": "5", "pin_name": "SS/REFIN", "pin_description": "双功能引脚。软启动功能:将电容器连接到VSNS - 引脚可对软启动时间进行编程。最短软启动时间(1.5ms)在内部是固定的。为避免在软启动电容器充电期间发生过冲,该引脚需要一个最小值为1nF的电容器。REFIN 功能:该器件始终将这个SS/REFIN 引脚上的电压作为控制环路的基准。内部基准电压可由该引脚上的外部直流电压源覆盖以用于跟踪应用。"}, {"pin_number": "6", "pin_name": "VSNS -", "pin_description": "用于远程电压检测配置的回路连接。它还用作内部基准的接地端。对于单端检测配置,短接至AGND。"}, {"pin_number": "7", "pin_name": "FB", "pin_description": "输出电压反馈输入。从VOUT到VSNS-(抽头至FB引脚)的电阻分压器可设置输出电压。"}, {"pin_number": "8", "pin_name": "EN", "pin_description": "启用引脚。使能引脚可开启或关闭直流/直流开关转换器。在启动前将EN引脚悬空会禁用转换器。EN引脚的建议运行条件为最大5.5V。请勿将EN引脚直接连接到VIN引脚。"}, {"pin_number": "9", "pin_name": "PGOOD", "pin_description": "开漏电源正常状态信号。当FB电压超出指定限值时,PGOOD在2µs延迟后变为低电平。"}, {"pin_number": "10, 21", "pin_name": "VIN", "pin_description": "集成功率MOSFET 对和内部LDO的电源输入引脚。应将VIN 引脚和PGND 引脚之间的去耦输入电容器尽可能靠近放置。"}, {"pin_number": "11, 12, 13, 14, 15, 16, 17, 18", "pin_name": "PGND", "pin_description": "内部低侧 MOSFET的电源接地端。至少需要将六个PGND 过孔尽可能靠近PGND 引脚放置。这样可以更大限度减小寄生阻抗并降低热阻。"}, {"pin_number": "19", "pin_name": "VCC", "pin_description": "内部 3V LDO 输出。可将3.3V 或更高电压的外部偏置连接到该引脚以减少内部LDO上的功率损耗。该引脚上的电压源为内部电路和栅极驱动器供电。从 VCC 引脚到PGND 引脚之间需要一个额定电压至少为6.3V的2.2µF陶瓷电容器作为去耦电容器,并且需要尽可能靠近放置。"}, {"pin_number": "20", "pin_name": "SW", "pin_description": "电源转换器的输出开关端子。将该引脚连接到输出电感器。"}]}], "datasheet_cn": {"name": "ZHCSL24D", "path": "未找到", "version": "REVISED JULY 2021", "release_date": "2021-07"}, "datasheet_en": {"name": "SNVSBB1", "path": "未找到", "version": "未找到", "release_date": "未找到"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 16, "min_input_voltage": 2.7, "max_output_voltage": 5.5, "min_output_voltage": 0.6, "max_output_current": 15, "max_switch_frequency": 1.1, "quiescent_current": 680, "high_side_mosfet_resistance": 10.2, "low_side_mosfet_resistance": 3.1, "over_current_protection_threshold": "15.1A - 21.4A (可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Eco-mode (跳跃模式)", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1, "output_reference_voltage": 0.6, "loop_control_mode": "D-CAP3", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2.7V", "max_output_voltage": "5.5V", "min_output_voltage": "0.6V", "max_output_current": "15A", "max_switch_frequency": "1.1MHz", "quiescent_current": "680µA", "high_side_mosfet_resistance": "10.2mΩ", "low_side_mosfet_resistance": "3.1mΩ", "over_current_protection_threshold": "18.4A (典型谷值, 可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Eco-mode (跳跃模式)", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.6V", "loop_control_mode": "D-CAP3"}}