{"part_number": "TPS5430", "manufacturer": "Texas Instruments", "country": "未找到", "manufacturing_status": "Active", "application_grade": "工业级", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS543x 3A、宽输入范围降压转换器", "features": ["宽输入电压范围: TPS5430: 5.5V 至 36V; TPS5431: 5.5V 至 23V", "高达 3A 的连续 (4A 峰值) 输出电流", "通过 100mΩ 集成式 MOSFET 开关实现高达 95% 的高效率", "宽输出电压范围: 可调节为低至 1.22V, 初始精度为 1.5%", "内部补偿可最大限度减少外部器件数量", "适用于小型滤波器尺寸的固定 500kHz 开关频率", "通过输入电压前馈改进线路调整和瞬态响应", "系统受过流限制、过压保护和热关断的保护", "-40°C 至 125°C 的工作结温范围", "采用小型热增强型 8 引脚 SO PowerPAD™ 集成电路封装", "使用 TPS5430 并借助 WEBENCH® Power Designer 创建定制设计"], "description": "TPS543x 是一款高输出电流 PWM 转换器,集成了低电阻、高侧 N 沟道 MOSFET。具有所列的特性的基板上还包括高性能电压误差放大器(可在瞬态条件下提供高稳压精度)、欠压锁定电路(用于防止在输入电压达到 5.5V 前启动)、内部设置的慢启动电路(用于限制浪涌电流)以及电压前馈电路(用于改进瞬态响应)。通过使用 ENA 引脚,关断电源电流通常可减少到 15µA。其他特性包括高电平有效使能端、过流限制、过压保护和热关断。为降低设计复杂性并减少外部元件数量,对 TPS543x 反馈环路进行内部补偿。TPS5431 可采用高达 23V 的电源轨运行。TPS5430 可调节多种电源,包括 24V 总线。TPS543x 器件采用热增强型且易于使用的 8 引脚 SOIC PowerPAD 集成电路封装。TI 提供评估模块和 Designer 软件工具,协助快速实现高性能电源设计,满足迫切的设备开发周期要求。", "applications": ["消费类: 机顶盒、DVD 显示屏、LCD 显示屏", "工业用和车载音频电源", "电池充电器、大功率 LED 电源", "12V 和 24V 分布式电源系统"], "ordering_information": [{"part_number": "TPS5430", "order_device": "TPS5430DDA", "status": "Active", "package_type": "SO PowerPAD", "package_code": "DDA", "carrier_description": "TUBE", "carrier_quantity": 75, "package_drawing_code": "DDA0008J", "marking": "5430", "pin_count": 8, "length": null, "width": null, "height": 1.7, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": null, "application_grade": null}, {"part_number": "TPS5430", "order_device": "TPS5430DDAR", "status": "Active", "package_type": "SO PowerPAD", "package_code": "DDA", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "package_drawing_code": "DDA0008J", "marking": "5430", "pin_count": 8, "length": null, "width": null, "height": 1.7, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": null, "application_grade": null}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS5430/31", "package_type": "DDA (HSOIC)", "pins": [{"pin_number": "1", "pin_name": "BOOT", "pin_description": "高侧 FET 栅极驱动器的升压电容器。从 BOOT 引脚至 PH 引脚连接一个 0.01µF 低 ESR 电容器。"}, {"pin_number": "2, 3", "pin_name": "NC", "pin_description": "内部未连接。"}, {"pin_number": "4", "pin_name": "VSENSE", "pin_description": "稳压器的反馈电压。连接到输出电压分压器。"}, {"pin_number": "5", "pin_name": "ENA", "pin_description": "导通和关闭控制。低于 0.5V, 器件停止切换。悬空引脚以启用。"}, {"pin_number": "6", "pin_name": "GND", "pin_description": "地。连接至 DAP。"}, {"pin_number": "7", "pin_name": "VIN", "pin_description": "输入电源电压。旁路 VIN 引脚至 GND 引脚靠近采用高质量、低 ESR 陶瓷电容器的器件封装。"}, {"pin_number": "8", "pin_name": "PH", "pin_description": "高侧功率 MOSFET 的源极。连接至外部电感器和二极管。"}, {"pin_number": "DAP", "pin_name": "DAP", "pin_description": "必须将 GND 引脚连接到外露焊盘才能正常运行。"}]}], "datasheet_cn": {"datasheet_name": "TPS5430, TPS5431", "datasheet_path": "ZHCSQS8K", "release_date": "2024-01", "version": "K"}, "datasheet_en": {"datasheet_name": "TPS5430, TPS5431", "datasheet_path": "SLVS632", "release_date": null, "version": null}, "family_comparison": "TPS5430和TPS5431的主要区别在于输入电压范围。TPS5430的输入电压范围为5.5V至36V，而TPS5431的输入电压范围为5.5V至23V。", "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 36, "min_input_voltage": 5.5, "max_output_voltage": "可调，受限于最大占空比和输入电压", "min_output_voltage": 1.221, "max_output_current": 3, "max_switch_frequency": 0.6, "quiescent_current": 2000, "high_side_mosfet_resistance": 100, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 5, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1.5, "output_reference_voltage": 1.221, "loop_control_mode": "电压模式", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "5.5V", "max_output_voltage": "可调，受限于最大占空比和输入电压", "min_output_voltage": "1.221V", "max_output_current": "3A", "max_switch_frequency": "600kHz", "quiescent_current": "2000μA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "5A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.5%", "output_reference_voltage": "1.221V", "loop_control_mode": "电压模式"}}