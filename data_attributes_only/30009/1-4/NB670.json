{"part_number": "NB670", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND (Not Recommended for New Designs)", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "NB670 24V, High Current Synchronous Buck Converter With LDO", "features": ["Wide 5V to 24V Operating Input Range", "3.3V Fixed Output Voltage", "Built-in 3.3V, 100mA LDO with Switches", "6A Continuous Output Current", "9A Peak Output Current", "300kHZ CLK for External Charge Pump", "Low RDS(ON) Internal Power MOSFETs", "Proprietary Switching Loss Reduction Technique", "Internal Soft Start", "Output Discharge", "500kHZ Switching Frequency", "OCP, OVP, UVP Protection and Thermal Shutdown", "Latch off Reset via EN or Power Cycle"], "description": "The NB670 is a fully integrated high frequency synchronous rectified step-down switch mode converter with 3.3V fixed output voltage. It offers very compact solution to achieve 6A continuous output current and 9A peak output current over a wide input supply range with excellent load and line regulation. The NB670 operates at high efficiency over a wide output current load range. Constant-On-Time (COT) control mode provides fast transient response and eases loop stabilization.\nUnder voltage lockout is internally set as 4.65 V. An open drain power good signal indicates the output is within its nominal voltage range.\nNB670 also provides a 3.3V LDO, which can be used to power the external peripheries, such as the keyboard controller in the laptop computer. A 300kHz CLK is also available; its output can be used to drive an external charge pump, generating gate drive voltage for the load switches without reducing the main converter's efficiency.\nFull protection features include OCP, OVP, UVP and thermal shut down.\nThe converter requires minimum number of external components and is available in QFN16 (3mmx3mm) package.", "applications": ["Laptop Computer", "Tablet PC", "Networking Systems", "Personal Video Recorders", "Flat Panel Television and Monitors", "Distributed Power Systems"], "ordering_information": [{"part_number": "NB670", "order_device": "NB670GQ", "status": "NRFND (Not Recommended for New Designs)", "package_type": "QFN-16", "package_code": "QFN-16 (3mmx3mm)", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "ADZ", "pin_count": "16", "length": "3", "width": "3", "height": "1.00", "pitch": "0.40", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "3.3", "application_grade": "Industrial"}, {"part_number": "NB670", "order_device": "NB670GQ-Z", "status": "NRFND (Not Recommended for New Designs)", "package_type": "QFN-16", "package_code": "QFN-16 (3mmx3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "ADZ", "pin_count": "16", "length": "3", "width": "3", "height": "1.00", "pitch": "0.40", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "3.3", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "NB670", "package_type": "QFN-16 (3mmx3mm)", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "Supply Voltage. The VIN pin supplies power for internal MOSFET and regulator. The NB670 operates from a +5V to +24V input rail. An input capacitor is needed to decouple the input rail. Use wide PCB traces and multiple vias to make the connection."}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power Ground. Use wide PCB traces and multiple vias to make the connection."}, {"pin_number": "3", "pin_name": "NC", "pin_description": "Not connected."}, {"pin_number": "4", "pin_name": "PG", "pin_description": "Power good output. The output of this pin is an open drain signal and is high if the output voltage is higher than 95% of the nominal voltage. There is a delay from Vout ≥ 95% to PGOOD goes high."}, {"pin_number": "5", "pin_name": "CLK", "pin_description": "300kHZ CLK output to drive the external charge pump"}, {"pin_number": "6", "pin_name": "LDO", "pin_description": "Internal 3.3V LDO output. Decouple with a minimum 4.7µF ceramic capacitor as close to the pin as possible. X7R or X5R grade dielectric ceramic capacitors are recommended for their stable temperature characteristics. Once the output voltage of the Buck regulator is ready, it will switch over the LDO output to save the power loss."}, {"pin_number": "7", "pin_name": "VOUT", "pin_description": "Output voltage sense. For the NB670, the output of the Buck regulator is fixed to 3.3V. VOUT pin is used to sense the output voltage of the Buck regulator, connect this pin to the output capacitor of the regulator directly. This pin also acts as the input of the 3.3V LDO switch over power input. Keep the VOUT sensing trace far away from the SW node. Vias should also be avoided on the VOUT sensing trace."}, {"pin_number": "8, 9, Exposed Pad 15, 16", "pin_name": "SW", "pin_description": "Switch Output. Connect this pin to the inductor and bootstrap capacitor. This pin is driven up to the VIN voltage by the high-side switch during the on-time of the PWM duty cycle. The inductor current drives the SW pin negative during the off-time. The on-resistance of the low-side switch and the internal diode fixes the negative voltage. Use wide and short PCB traces to make the connection. Try to minimize the area of the SW pattern."}, {"pin_number": "10", "pin_name": "BST", "pin_description": "Bootstrap. A capacitor connected between SW and BST pins is required to form a floating supply across the high-side switch driver."}, {"pin_number": "11", "pin_name": "VCC", "pin_description": "Internal 5V LDO output. The driver and control circuits are powered from this voltage. Decouple with a minimum 1µF ceramic capacitor as close to the pin as possible. X7R or X5R grade dielectric ceramic capacitors are recommended for their stable temperature characteristics."}, {"pin_number": "12", "pin_name": "ENLDO", "pin_description": "100mA LDO and VCC enable pin. ENLDO is internally pulled up to high. Leave this pin open to enable the LDO. Drive it low to turn off all the regulators."}, {"pin_number": "13", "pin_name": "EN", "pin_description": "Buck regulator and charge pump clock enable pin. EN is a digital input that turns the Buck regulator and CLK on or off. When the power supply of the control circuit is ready, drive EN high to turn on the Buck regulator and charge pump clock, drive it low to turn them off."}, {"pin_number": "14", "pin_name": "AGND", "pin_description": "Analog ground. The internal reference is referred to AGND."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "NB670", "path": "用户上传的PDF", "release_date": "2014-08-27", "version": "1.02"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "22", "min_input_voltage": "5", "max_output_voltage": "3.3", "min_output_voltage": "3.3", "max_output_current": "6", "max_switch_frequency": "0.6", "quiescent_current": "220", "high_side_mosfet_resistance": "30", "low_side_mosfet_resistance": "15", "over_current_protection_threshold": "8.5", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1.94", "output_reference_voltage": "0.6", "loop_control_mode": "固定导通时间控制", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "5V", "max_output_voltage": "3.3V", "min_output_voltage": "3.3V", "max_output_current": "6A", "max_switch_frequency": "0.5MHz", "quiescent_current": "220µA", "high_side_mosfet_resistance": "30mΩ", "low_side_mosfet_resistance": "15mΩ", "over_current_protection_threshold": "8.5A", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PSM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1.94%", "output_reference_voltage": "0.6V", "loop_control_mode": "固定导通时间控制"}}