{"part_number": "MP2312", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "24V, 2.1A High Efficiency Synchronous Step-down Converter", "features": ["Wide 5V to 24V Operating Input Range", "2.1A Continuous Output Current", "Low RDS(ON) Internal Power MOSFETs", "Proprietary Switching Loss Reduction Technique", "1% Reference Voltage In Room Temperature", "7ms Internal Soft Start", "Output Discharge", "500kHz Switching Frequency", "Hiccup OCP Protection and Thermal Shutdown", "Auto Retry OVP Protection", "Output Adjustable from 0.604V to 5.5V"], "description": "The MP2312 is a fully integrated high frequency synchronous rectified step-down switch mode converter. It offers very compact solutions to achieve 2.1A continuous output current over a wide input supply range with excellent load and line regulation. The MP2312 operates at high efficiency over a wide output current load range. Constant-On-Time (COT) control mode provides fast transient response and eases loop stabilization. Under voltage lockout is internally set as 4.6 V, An open drain power good signal indicates the output is within its nominal voltage range. Full protection features include OCP and thermal shut down. The converter requires minimum number of external components and is available in QFN16 (3x3mm) package.", "applications": ["Laptop Computer", "Tablet PC", "Networking Systems", "Personal Video Recorders", "Flat Panel Television and Monitors", "Distributed Power Systems"], "ordering_information": [{"part_number": "MP2312", "order_device": "MP2312GQ", "status": "NRFND", "package_type": "QFN16(3X3mm)", "package_code": "QFN16", "carrier_description": "Tape&Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "AHU", "pin_count": 16, "length": 3.0, "width": 3.0, "height": "未找到", "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "0.604-5.5V", "application_grade": "Automotive"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "MP2312", "package_type": "QFN16(3x3mm)", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "Supply Voltage. The IN pin supplies power for internal MOSFET and regulator. The MP2312 operate from a +5V to +22V input rail. An input capacitor is needed to decouple the input rail. Use wide PCB traces and multiple vias to make the connection."}, {"pin_number": "2", "pin_name": "PGND", "pin_description": "Power Ground. Use wide PCB traces and multiple vias to make the connection"}, {"pin_number": "3, 5, 6", "pin_name": "NC", "pin_description": "No Connection."}, {"pin_number": "4", "pin_name": "PG", "pin_description": "Power good output, the output of this pin is an open drain signal and is high if the output voltage is higher than 95% of the nominal voltage. There is a delay from FB ≥ 95% to PGOOD goes high."}, {"pin_number": "7", "pin_name": "VOUT", "pin_description": "VOUT pin is used to sense the output voltage of the Buck regulator, connect this pin to the output capacitor of the regulator directly."}, {"pin_number": "8, 9, Exposed Pad 15, 16", "pin_name": "SW", "pin_description": "Switch Output. Connect this pin to the inductor and bootstrap capacitor. This pin is driven up to the VIN voltage by the high-side switch during the on-time of the PWM duty cycle. The inductor current drives the SW pin negative during the off-time. The on-resistance of the low-side switch and the internal diode fixes the negative voltage. Use wide and short PCB traces to make the connection. Try to minimize the area of the SW pattern."}, {"pin_number": "10", "pin_name": "BST", "pin_description": "Bootstrap. A capacitor connected between SW and BS pins is required to form a floating supply across the high-side switch driver."}, {"pin_number": "11", "pin_name": "VCC", "pin_description": "Internal 5V LDO output. The driver and control circuits are powered from this voltage. Decouple with a minimum 1µF ceramic capacitor as close to the pin as possible. X7R or X5R grade dielectric ceramic capacitors are recommended for their stable temperature characteristics."}, {"pin_number": "12", "pin_name": "FB", "pin_description": "Feedback. An external resistor divider from the output to GND, tapped to the FB pin, sets the output voltage. It is recommended to place the resistor divider as close to FB pin as possible. Vias should be avoided on the FB traces."}, {"pin_number": "13", "pin_name": "EN", "pin_description": "Enable pin. EN is a digital input that turns the regulator on or off. Drive EN high to turn on the regulator, drive it low to turn it off. Connect EN with VIN through a pull-up resistor or a resistive voltage divider for automatic startup. Do not float this pin."}, {"pin_number": "14", "pin_name": "AGND", "pin_description": "Analog ground. The internal reference is referred to AGND. Connect the GND of the FB divider resistor to AGND for better load regulation."}]}], "datasheet_en": {"name": "MP2312 Rev. 1.01", "path": "用户上传文件", "release_date": "2014-01-21", "version": "1.01"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 22.0, "min_input_voltage": 5.0, "max_output_voltage": 5.5, "min_output_voltage": 0.604, "max_output_current": 2.1, "max_switch_frequency": 0.55, "quiescent_current": 220.0, "high_side_mosfet_resistance": 38.0, "low_side_mosfet_resistance": 15.0, "over_current_protection_threshold": 2.1, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy_percentage": 1.0, "output_reference_voltage_value": 0.604, "loop_control_mode": "固定导通时间控制", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22V", "min_input_voltage": "5V", "max_output_voltage": "5.5V", "min_output_voltage": "0.604V", "max_output_current": "2.1A", "max_switch_frequency": "550kHz", "quiescent_current": "220µA", "high_side_mosfet_resistance": "38mΩ", "low_side_mosfet_resistance": "15mΩ", "over_current_protection_threshold": "2.1A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM/PSM/Skip", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.604V", "loop_control_mode": "固定导通时间控制"}}