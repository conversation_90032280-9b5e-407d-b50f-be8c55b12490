{"part_number": "MPQ4431", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片", "part_number_title": "36V, 1A, Low Quiescent Current, Synchronous, Step-Down Converter AEC-Q100 Qualified", "features": ["Wide 3.3V to 36V Operating Input Range", "1A Continuous Output Current", "1μA Low Shutdown Mode Current", "10μA Sleep Mode Quiescent Current", "Internal 90mΩ High-Side and 80mΩ Low-Side MOSFETS", "350kHz to 2.5MHz Programmable Switching Frequency", "Fixed Output Options: 3.3V, 3.8V, 5V", "Synchronize to External Clock", "Selectable In-Phase or 180° Out-of-Phase", "Power Good (PG) Indicator", "Programmable Soft-Start (SS) Time", "80ns Minimum On Time", "Selectable Forced CCM or AAM", "Low Dropout Mode", "Over-Current Protection (OCP) with Valley-Current Detection and Hiccup", "Available in a QFN-16 (3mmx4mm) Package", "Available with Wettable Flank", "AEC-Q100 Grade-1"], "description": "The MPQ4431 is a frequency-programmable (350kHz to 2.5MHz), synchronous, step-down switching regulator with integrated, internal, high-side and low-side power MOSFETs. It provides up to 1A of highly efficient output current with current mode control for fast loop response. The wide 3.3V to 36V input range accommodates a variety of step-down applications in automotive input environments and is ideal for battery-powered applications due to its extremely low quiescent current. The MPQ4431 employs advanced asynchronous mode (AAM), which helps achieve high efficiency in light-load condition by scaling down the switching frequency to reduce switching and gate driving losses. Standard features include soft start (SS), external clock synchronization, enable (EN) control, and a power good (PG) indicator. High-duty cycle and low dropout mode are provided for automotive cold-crank condition. Over-current protection (OCP) with valley-current detection is employed to prevent the inductor current from running away. Hiccup mode reduces the average current in short-circuit condition greatly. Thermal shutdown provides reliable and fault-tolerant operation. The MPQ4431 is available in a QFN-16 (3mmx4mm) package.", "applications": ["Automotive Systems", "Industrial Power Systems"], "ordering_information": [{"part_number": "MPQ4431", "order_device": "MPQ4431GL", "status": "Active", "package_type": "QFN-16", "package_code": "QFN-16 (3mmx4mm)", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MO-220", "marking": "MPYW 4431 LLL", "pin_count": 16, "length": 4.0, "width": 3.0, "height": 1.0, "pitch": 0.6, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "Industrial"}, {"part_number": "MPQ4431", "order_device": "MPQ4431GL-AEC1", "status": "Active", "package_type": "QFN-16", "package_code": "QFN-16 (3mmx4mm)", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MO-220", "marking": "MPYW 4431 LLL", "pin_count": 16, "length": 4.0, "width": 3.0, "height": 1.0, "pitch": 0.6, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "Automotive"}, {"part_number": "MPQ4431", "order_device": "MPQ4431GLE-AEC1", "status": "Active", "package_type": "QFN-16 (Wettable Flank)", "package_code": "QFN-16 (3mmx4mm)", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MO-220", "marking": "MPYW 4431 LLL E", "pin_count": 16, "length": 4.0, "width": 3.0, "height": 1.0, "pitch": 0.6, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "Automotive"}, {"part_number": "MPQ4431", "order_device": "MPQ4431GLE-33-AEC1", "status": "Under Qualification", "package_type": "QFN-16 (Wettable Flank)", "package_code": "QFN-16 (3mmx4mm)", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MO-220", "marking": "MPYW 4431 LLL E33", "pin_count": 16, "length": 4.0, "width": 3.0, "height": 1.0, "pitch": 0.6, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": 3.3, "application_grade": "Automotive"}, {"part_number": "MPQ4431", "order_device": "MPQ4431GLE-5-AEC1", "status": "Under Qualification", "package_type": "QFN-16 (Wettable Flank)", "package_code": "QFN-16 (3mmx4mm)", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MO-220", "marking": "MPYW 4431 LLL E5", "pin_count": 16, "length": 4.0, "width": 3.0, "height": 1.0, "pitch": 0.6, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": 5.0, "application_grade": "Automotive"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "MPQ4431 (Adjustable)", "package_type": "QFN-16", "pins": [{"pin_number": "1", "pin_name": "PHASE", "pin_description": "Selectable in-phase or 180° out-of-phase of SYNC input. Drive PHASE high to be in-phase. Drive PHASE low to be 180° out-of-phase. Recommend to connect this pin to GND if not used."}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Input supply. VIN supplies power to all of the internal control circuitries and the power switch connected to SW. A decoupling capacitor to ground must be placed close to VIN to minimize switching spikes."}, {"pin_number": "3, 10", "pin_name": "SW", "pin_description": "Switch node. SW is the output of the internal power switch. Pin 3 and Pin 10 are internally connected."}, {"pin_number": "4, 9", "pin_name": "PGND", "pin_description": "Power ground. PGND is the reference ground of the power device and requires careful consideration during PCB layout. For best results, connect PGND with copper pours and vias."}, {"pin_number": "5", "pin_name": "EN", "pin_description": "Enable. Pull EN below the specified threshold to shut the chip down. Pull EN above the specified threshold to enable the chip."}, {"pin_number": "6", "pin_name": "SYNC", "pin_description": "Synchronize. Apply a 350kHz to 2.5MHz clock signal to SYNC to synchronize the internal oscillator frequency to the external clock. The external clock should be at least 250kHz larger than the RFREQ set frequency. SYNC can also be used to select forced continuous conduction mode (CCM) or advanced asynchronous mode (AAM). Before the chip starts up, drive S<PERSON><PERSON> low or leave SYNC floating to choose AAM, and drive SY<PERSON> high to external power source or pull up SYNC to VCC directly to set the part forced CCM mode."}, {"pin_number": "7", "pin_name": "PG", "pin_description": "Power good indicator. The output of PG is an open drain and goes high if the output voltage is within ±10% of the nominal voltage. Float PG if not used."}, {"pin_number": "8", "pin_name": "BIAS", "pin_description": "External power supply for the internal regulator. Connect BIAS to an external power supply (5V ≤ VBIAS ≤ 18V) to reduce power dissipation and increase efficiency. Float BIAS or connect BIAS to ground if not used."}, {"pin_number": "11", "pin_name": "BST", "pin_description": "Bootstrap. BST is the positive power supply for the high-side MOSFET driver connected to SW. Connect a bypass capacitor between BST and SW."}, {"pin_number": "12", "pin_name": "VCC", "pin_description": "Internal bias supply. VCC supplies power to the internal control circuit and gate drivers. A ≥1µF decoupling capacitor to ground is required close to VCC."}, {"pin_number": "13", "pin_name": "AGND", "pin_description": "Analog ground. AGND is the reference ground of the logic circuit."}, {"pin_number": "14", "pin_name": "SS", "pin_description": "Optional external soft-start time setting. Connect an external capacitor between this pin and GND to set soft-start time externally. The MPQ4431 sources 10µA from SS to the soft-start capacitor during start-up. As the SS voltage rises, the feedback threshold voltage increases to limit inrush current during start-up. Floating the pin will activate the internal 0.7ms soft-start setting."}, {"pin_number": "15", "pin_name": "FB", "pin_description": "Feedback input for output adjustable version. Connect FB to the tap of an external resistor divider from the output to AGND to set the output voltage. The feedback threshold voltage is 0.8V. Place the resistor divider as close to FB as possible. Avoid placing vias on the FB traces."}, {"pin_number": "16", "pin_name": "FREQ", "pin_description": "Switching frequency program. Connect a resistor from FREQ to ground to set the switching frequency."}]}, {"product_part_number": "MPQ4431 (Fixed)", "package_type": "QFN-16", "pins": [{"pin_number": "15", "pin_name": "VOUT", "pin_description": "Regulated output voltage for fixed output version. Connect VOUT pin to the output directly."}]}], "datasheet_en": {"datasheet_name": "MPQ4431-36V, 1A, LOW IQ, SYNC, STEP-DOWN CONVERTER, AEC-Q100", "datasheet_path": "用户上传", "release_date": "2019-10-31", "version": "1.02"}, "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.3V", "max_output_voltage": "36V", "min_output_voltage": "0.8V", "max_output_current": "1A", "max_switch_frequency": "2.5MHz", "quiescent_current": "10µA", "high_side_mosfet_resistance": "90mΩ", "low_side_mosfet_resistance": "80mΩ", "over_current_protection_threshold": "2.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "AAM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "2.0%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}