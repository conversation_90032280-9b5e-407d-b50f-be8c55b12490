{"part_number": "TPS51220A-Q1", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压(<PERSON>)控制器", "part_number_title": "固定频率99%占空比峰值电流模式笔记本系统功率控制器", "features": ["符合汽车应用要求", "输入电压范围: 4.5V 至 32V", "输出电压范围: 1V 至 12V", "可选轻载操作 (连续/自动跳过/Out-Of-Audio™ 跳过)", "可编程降压补偿", "电压伺服可调节软启动", "200kHz 至 1MHz 固定频率 PWM", "可选电流/D-CAP™ 模式架构", "通道间的 180° 相移", "电阻或电感器 DCR 电流感应", "自适应零交叉电路", "每个通道的电源良好输出", "OCL/OVP/UVP/UVLO 保护 (OVP 禁用选项)", "热关断 (非锁闭)", "输出放电功能 (禁用选项)", "闭锁电流超过 100mA, 符合 JESD78 I 类标准", "集成自举 MOSFET 开关", "QFN-32 (RTV) 封装"], "description": "TPS51220A-Q1 是一个带有两个 LDO 的双路同步降压稳压器控制器它针对 5V/3.3V 系统控制器进行过优化, 使设计者能够经济高效地完善 2 节电池到 4 节电池的笔记本系统电源。TPS51220A-Q1 支持高效、快速瞬态响应和 99% 的工作占空比。它支持 4.5V 到 32V 的电源输入电压以及 1V 到 12V 的输出电压。可以根据应用来选择两种类型的控制方案。峰值电流模式支持较低 ESR 电容器及输出精度的稳定运行。D-CAP 模式支持快速瞬态响应。高占空比 (99%) 运行和宽输入/输出电压范围支持小型移动 PC 和各种其它应用的灵活设计。固定频率可通过电阻在 200 kHz 到 1 MHz 之间进行调节, 而每个通道运行 180° 相移。TPS51220A-Q1 可以与外部时钟同步, 交错比可通过其自身占空比调节。TPS51220A-Q1 采用 32 引脚 5×5/4×4 QFN 封装, 可适应–40℃ 到 105℃ 的环境。", "applications": ["I/O 总线", "液晶电视、多媒体框架产品 (MFP) 的负载点"], "ordering_information": [{"part_number": "TPS51220A-Q1", "order_device": "TPS51220ATRTVRQ1", "status": "Active", "package_type": "QFN", "package_code": "RTV", "carrier_description": "Tape & Reel", "carrier_quantity": "3000", "package_drawing_code": "RTV", "marking": "51220AT", "pin_count": "32", "length": "5.0", "width": "5.0", "height": "0.8", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "105", "output_voltage": "可调", "application_grade": "Automotive"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS51220A-Q1", "package_type": "QFN-32", "pins": [{"pin_number": "1", "pin_name": "DRVH1", "pin_description": "高侧MOSFET栅极驱动器输出。源1.7Ω，沉1.0Ω，SW节点参考的浮动驱动器。驱动电压对应于VBST到SW的电压。"}, {"pin_number": "2", "pin_name": "V5SW", "pin_description": "VREG5切换电源输入引脚。当EN1为高电平，PGOOD1指示良好且V5SW电压高于4.83V时，切换功能被启用。"}, {"pin_number": "3", "pin_name": "RF", "pin_description": "频率设置引脚。连接一个频率设置电阻到（信号）GND。连接到外部时钟以进行同步。"}, {"pin_number": "4", "pin_name": "EN1", "pin_description": "通道1 SMPS使能引脚。开启时，施加大于0.55V且小于6V的电压，或保持浮空。连接到GND以禁用。可连接可调软启动电容。"}, {"pin_number": "5", "pin_name": "PGOOD1", "pin_description": "通道1的电源良好窗口比较器输出。推荐施加电压应小于6V，推荐上拉电阻值为100kΩ至1MΩ。"}, {"pin_number": "6", "pin_name": "SKIPSEL1", "pin_description": "跳跃模式选择引脚。GND：连续导通模式；VREF2：自动跳跃；VREG3：OOA自动跳跃，最大7次跳跃；VREG5：OOA自动跳跃，最大15次跳跃。"}, {"pin_number": "7", "pin_name": "CSP1", "pin_description": "电流检测比较器输入（+）。应使用高质量X5R或X7R陶瓷电容的RC网络来提取DCR上的压降。"}, {"pin_number": "8", "pin_name": "CSN1", "pin_description": "电流检测比较器输入（-）。用作5V或更高输出电压设置的电流检测电路的电源。也用作输出放电端子。"}, {"pin_number": "9", "pin_name": "VFB1", "pin_description": "SMPS电压反馈输入。连接反馈电阻分压器，并应参考（信号）GND。"}, {"pin_number": "10", "pin_name": "COMP1", "pin_description": "电流模式的环路补偿引脚（误差放大器输出）。"}, {"pin_number": "11", "pin_name": "FUNC", "pin_description": "控制架构和OVP功能选择引脚。GND：电流模式，OVP使能；VREF2：D-CAP模式，OVP禁用；VREG3：D-CAP模式，OVP使能；VREG5：电流模式，OVP禁用。"}, {"pin_number": "12", "pin_name": "EN", "pin_description": "VREF2和VREG5线性稳压器使能引脚。开启时，施加大于1.2V且小于6V的电压。连接到GND以禁用。"}, {"pin_number": "13", "pin_name": "VREF2", "pin_description": "2V参考输出。用0.22μF的陶瓷电容旁路到（信号）GND。"}, {"pin_number": "14", "pin_name": "TRIP", "pin_description": "过流触发电平和放电模式选择引脚。"}, {"pin_number": "15", "pin_name": "COMP2", "pin_description": "电流模式的环路补偿引脚（误差放大器输出）。"}, {"pin_number": "16", "pin_name": "VFB2", "pin_description": "SMPS电压反馈输入。连接反馈电阻分压器，并应参考（信号）GND。"}, {"pin_number": "17", "pin_name": "CSN2", "pin_description": "电流检测比较器输入（-）。"}, {"pin_number": "18", "pin_name": "CSP2", "pin_description": "电流检测比较器输入（+）。"}, {"pin_number": "19", "pin_name": "SKIPSEL2", "pin_description": "跳跃模式选择引脚。"}, {"pin_number": "20", "pin_name": "PGOOD2", "pin_description": "通道2的电源良好窗口比较器输出。"}, {"pin_number": "21", "pin_name": "EN2", "pin_description": "通道2 SMPS使能引脚。"}, {"pin_number": "22", "pin_name": "VREG3", "pin_description": "始终有效的3.3V，10mA低压差线性稳压器输出。用大于1μF的陶瓷电容旁路到（信号）GND。"}, {"pin_number": "23", "pin_name": "VIN", "pin_description": "5V和3.3V线性稳压器的电源输入。通常连接到VBAT。"}, {"pin_number": "24", "pin_name": "DRVH2", "pin_description": "高侧MOSFET栅极驱动器输出。"}, {"pin_number": "25", "pin_name": "SW2", "pin_description": "高侧MOSFET栅极驱动器返回。"}, {"pin_number": "26", "pin_name": "VBST2", "pin_description": "高侧N沟道FET驱动器的电源输入（自举端子）。"}, {"pin_number": "27", "pin_name": "DRVL2", "pin_description": "低侧MOSFET栅极驱动器输出。源1.3Ω，沉0.7Ω，GND参考驱动器。"}, {"pin_number": "28", "pin_name": "GND", "pin_description": "地。"}, {"pin_number": "29", "pin_name": "VREG5", "pin_description": "5V，100mA低压差线性稳压器输出。用10μF陶瓷电容旁路到（电源）GND。"}, {"pin_number": "30", "pin_name": "DRVL1", "pin_description": "低侧MOSFET栅极驱动器输出。"}, {"pin_number": "31", "pin_name": "VBST1", "pin_description": "高侧N沟道FET驱动器的电源输入（自举端子）。"}, {"pin_number": "32", "pin_name": "SW1", "pin_description": "高侧MOSFET栅极驱动器返回。"}]}], "datasheet_cn": {"name": "ZHCS083-MARCH 2011", "path": "用户上传的PDF文件", "release_date": "2011-03", "version": "ZHCS083"}, "datasheet_en": {"name": "SLUSAI2", "path": "未找到", "release_date": "未找到", "version": "SLUSAI2"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "32V", "min_input_voltage": "4.5V", "max_output_voltage": "12V", "min_output_voltage": "1V", "max_output_current": "依赖外部元件", "max_switch_frequency": "1MHz", "quiescent_current": "80μA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM/PSM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "无", "input_under_voltage_protection": "欠压锁定", "output_over_voltage_protection": "锁存", "output_under_voltage_protection": "锁存", "output_over_load_protection": "锁存", "output_short_circuit_protection": "锁存", "over_temperature_protection": "自动重启", "hundred_percent_duty_cycle": "Yes", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式, D-CAP模式"}}