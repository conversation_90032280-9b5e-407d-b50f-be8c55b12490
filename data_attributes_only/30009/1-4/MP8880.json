{"part_number": "MP8880", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压", "part_number_title": "60V, 4A, High-Efficiency, Digital, Configurable, Synchronous Step-Down Converter with PMBus Interface", "features": ["Wide 4V to 60V Input Voltage (VIN) Range", "Wide 0.6V to 48V Output Voltage (VOUT) Range", "4A Output Current (I)", "Can be Paralleled with Up to 4 Phases", "Supports PD3.1 EPR 240W and AVS", "Configurable Constant Switching Frequency (fsw) (150kHz to 2.2MHz) with External Clock Synchronization", "Internal 60mΩ/43mΩ Low RDS(ON) MOSFETs", "97.5% Peak Efficiency", "Frequency Spread Spectrum for Low EMI", "Power Good and Fault Indication", "Output Over-Voltage Protection (OVP), Under-Voltage Protection (UVP), Over Current Protection (OCP), and Over-Temperature Protection (OTP)", "Low-Dropout Mode", "Telemetry Readback Includes VIN, VOUT, IOUT and Faults", "Configurable MTP Parameters: VOUT, fsw, Compensation Network, Output OCP, OVP, and UVP Thresholds, Input OVP and UVP Thresholds, PWM/AAM Mode", "CRC Protection for MTP Integrity", "Available in a QFN-20 (4mmx5mm) Package"], "description": "The MP8880 is a high-frequency, synchronous rectified step-down converter with a PMBus control interface. It can achieve up to 4A of continuous output current (IOUT), with excellent load and line regulation across the wide input voltage (VIN) supply range. The output voltage (VOUT) can be controlled on-the-fly via the PMBus serial interface. VOUT can be set between 0.6V and 24V in internal feedback divider mode, with a 48V maximum in external feedback divider mode. The voltage slew rate, switching frequency (fsw), and power-save mode can also be selected via the PMBus interface. Current mode operation provides fast transient response and eases loop stabilization. Full protection features include under-voltage lockout (UVLO), over-voltage protection (OVP), over-current protection (OCP), and thermal shutdown. The MP8880 supports multiple-time programmable (MTP) memory to provide flexible configurations. The device can be paralleled with up to 4 phases for applications with a higher IOUT. The integrated, internal high-side and low-side power MOSFETs (HS-FETs and LS-FETs, respectively) provide high efficiency without the use of an external Schottky diode. With an internal feedback divider and compensation, the MP8880 offers a very compact solution with a minimal number of readily available, standard external components. It is available in a QFN-20 (4mmx5mm) package.", "applications": ["DC/DC Power Systems", "USB PD3.1 Extended Power Ranges", "E-Bike Power", "High Input Power Systems"], "ordering_information": [{"part_number": "MP8880", "order_device": "MP8880GV-0200", "status": "NRFND", "package_type": "QFN-20", "package_code": "QFN-20 (4mmx5mm)", "carrier_description": "Tape & Reel", "carrier_quantity": 5000, "package_drawing_code": "未找到", "marking": "MPSYWW MP8880 LLLLLL", "pin_count": 20, "length": 4, "width": 5, "height": "未找到", "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Configurable", "application_grade": "Industrial"}, {"part_number": "MP8880", "order_device": "MP8880GV-0201", "status": "NRFND", "package_type": "QFN-20", "package_code": "QFN-20 (4mmx5mm)", "carrier_description": "Tape & Reel", "carrier_quantity": 5000, "package_drawing_code": "未找到", "marking": "MPSYWW MP8880 LLLLLL", "pin_count": 20, "length": 4, "width": 5, "height": "未找到", "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Configurable", "application_grade": "Industrial"}, {"part_number": "MP8880", "order_device": "MP8880GV-0209", "status": "NRFND", "package_type": "QFN-20", "package_code": "QFN-20 (4mmx5mm)", "carrier_description": "Tape & Reel", "carrier_quantity": 5000, "package_drawing_code": "未找到", "marking": "MPSYWW MP8880 LLLLLL", "pin_count": 20, "length": 4, "width": 5, "height": "未找到", "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Configurable", "application_grade": "Industrial"}, {"part_number": "MP8880", "order_device": "MP8880GVE-xxxx", "status": "NRFND", "package_type": "QFN-20", "package_code": "QFN-20 (4mmx5mm)", "carrier_description": "Tape & Reel", "carrier_quantity": 5000, "package_drawing_code": "未找到", "marking": "MPSYWW MP8880 LLLLLL E", "pin_count": 20, "length": 4, "width": 5, "height": "未找到", "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Configurable", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MP8880", "package_type": "QFN-20", "pins": [{"pin_number": "1", "pin_name": "VOUT", "pin_description": "Sense input for the output voltage. The VOUT pin is also used for the VOUT bias input. When VOUT > 4.8V, VCC can be supplied power through this pin. The VOUT bias function can be selected via the registers."}, {"pin_number": "2", "pin_name": "/FT", "pin_description": "Fault indicator. The /FT pin is pulled down if VIN OVP, VOUT OVP or UVP, or OCP occurs. The MFR_FAULT_CTRL register can determine which events control /FT. /FT is an open-drain output that requires an external pull-up resistor. Pull /FT up to VCC or float this pin if it is not used. /FT is multi-function pin that can be used for multi-phase configurations."}, {"pin_number": "3", "pin_name": "PG", "pin_description": "Power good indicator. PG is pulled down if VOUT OVP or UVP occurs. PG is an open-drain output that requires an external pull-up resistor. Connect PG to GND or float this pin if it is not used."}, {"pin_number": "4, 11", "pin_name": "PGND", "pin_description": "Power ground. PGND is the reference ground of the regulated output voltage. Connect these pins to larger copper areas at the negative terminals of the input and output capacitors."}, {"pin_number": "5, 10", "pin_name": "VIN", "pin_description": "Supply voltage. The VIN pin supplies all power to the converter. Place a decoupling capacitor from VIN to ground, and as close as possible to the IC to reduce switching spikes. Make the VIN connection using a wide PCB trace."}, {"pin_number": "6", "pin_name": "BST", "pin_description": "Bootstrap. Connect a capacitor between the SW and BST pins to form a floating supply across the high-side switch driver."}, {"pin_number": "7, 8", "pin_name": "SW", "pin_description": "Switch output. Use a wide PCB trace to make the connection between the SW pins and the inductor."}, {"pin_number": "9", "pin_name": "EN", "pin_description": "Enable. Drive EN high to turn on the device; drive EN low to turn off the device. Do not float the EN pin."}, {"pin_number": "12", "pin_name": "ADDR", "pin_description": "Address setting for the PMBus. ADDR sets the lower 3 bits of the PMBus address with a resistor connected to GND (see Table 2 on page 21)."}, {"pin_number": "13", "pin_name": "ALERTB", "pin_description": "Alert. ALERTB is pulled down if any fault or warning is indicated by STATUS_WORD (0x79). This pin is an open-drain output that requires an external pull-up resistor. Connect ALERTB to GND or float this pin if it is not used."}, {"pin_number": "14", "pin_name": "SDA", "pin_description": "PMBus serial data. Pull the SDA pin up to VCC if SDA is not used."}, {"pin_number": "15", "pin_name": "SCL", "pin_description": "PMBus serial clock. Pull the SCL pin up to VCC if SCL is not used."}, {"pin_number": "16", "pin_name": "SYNC", "pin_description": "Synchronized to external clock signal. The SYNC pin can be configured as the sync input or sync output via the PMBus."}, {"pin_number": "17", "pin_name": "VCC", "pin_description": "Internal 4.9V LDO regulator output. Decouple VCC with a 1µF capacitor."}, {"pin_number": "18", "pin_name": "AGND", "pin_description": "Signal ground. AGND is the ground for the internal logic and signal circuit. AGND is not internally connected to PGND, but ensure that AGND is connected to PGND in the PCB layout."}, {"pin_number": "19", "pin_name": "COMP", "pin_description": "Compensation for parallel operation. For multi-phase applications, connect all COMP pins together. Float COMP for single-phase applications."}, {"pin_number": "20", "pin_name": "VFB", "pin_description": "Feedback voltage input for external feedback divider mode. Connect VFB to a voltage divider to set the initial output voltage in external feedback divider mode. VFB should be floating or connected to GND in internal feedback divider mode."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MP8880.pdf", "datasheet_path": "local", "release_date": "2022-01-03", "version": "1.0"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 60, "min_input_voltage": 4, "max_output_voltage": 48, "min_output_voltage": 0.6, "max_output_current": 4, "max_switch_frequency": 2.2, "quiescent_current": 93, "high_side_mosfet_resistance": 60, "low_side_mosfet_resistance": 43, "over_current_protection_threshold": 5.8, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "未找到", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": 2.6, "output_reference_voltage": "未找到", "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "60V", "min_input_voltage": "4V", "max_output_voltage": "48V", "min_output_voltage": "0.6V", "max_output_current": "4A", "max_switch_frequency": "2.2MHz", "quiescent_current": "93µA", "high_side_mosfet_resistance": "60mΩ", "low_side_mosfet_resistance": "43mΩ", "over_current_protection_threshold": "5.8A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "AAM/FCCM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "1V", "loop_control_mode": "峰值电流模式"}}