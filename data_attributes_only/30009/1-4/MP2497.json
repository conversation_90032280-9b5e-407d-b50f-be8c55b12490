{"part_number": "MP2497", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "3A, 50V, 100kHz Step-Down Converter with Programmable Output OVP Threshold", "features": ["Wide 4.5V to 50V Operating Input Range", "Programmable Output Over Voltage Protection", "Output Adjustable from 0.8V to 25V", "0.15Ω Internal Power MOSFET Switch", "Internal 4ms Soft Start", "Stable with Low ESR Output Ceramic Capacitors", "Fixed 100kHz Frequency", "Low EMI Signature", "Thermal Shutdown", "Output Line Drop Compensation", "Hiccup Circuit Limit and Short Circuit Protection", "Available in SOIC8 and SOIC8E Package"], "description": "The MP2497 is a monolithic step-down switch mode converter with a programmable output current limit. It achieves 3A continuous output current over a wide input supply range with excellent load and line regulation. An internal 2~4ms soft start prevents inrush current at turning on. And it is capable of providing output line drop compensation. MP2497 achieves low EMI signature with well controlled switching edges. Fault condition protection includes hiccup current limit and short circuit protection, programmable output over voltage protection and thermal shutdown. The MP2497 requires a minimum number of readily available standard external components. The MP2497 is available in SOIC8 and SOIC8E package.", "applications": ["USB Power Supplies", "Automotive Cigarette Lighter Adapters", "Power Supply for Linear Chargers"], "ordering_information": [{"part_number": "MP2497", "order_device": "MP2497DS", "status": "Active", "package_type": "SOIC8", "package_code": "SOIC8", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MS012, VARIATION AA", "marking": "MP2497", "pin_count": 8, "length": 4.9, "width": 3.9, "height": 1.55, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": null, "application_grade": "Automotive"}, {"part_number": "MP2497", "order_device": "MP2497DN", "status": "Active", "package_type": "SOIC8E", "package_code": "SOIC8E", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MS-012, VARIATION BA", "marking": "MP2497", "pin_count": 8, "length": 4.9, "width": 3.9, "height": 1.5, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": null, "application_grade": "Automotive"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "MP2497", "package_type": "SOIC8/SOIC8E", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "Supply Voltage. The MP2497 operates from a +4.5V to +50V unregulated input. CIN is needed to prevent large voltage spikes from appearing at the input. Put CIN as close to the IC as possible. It is the drain of the internal power device and power supply for the whole chip."}, {"pin_number": "2", "pin_name": "GND, Exposed Pad", "pin_description": "Ground. This pin is the voltage reference for the regulated output voltage. For this reason care must be taken in its layout. This node should be placed outside of the D1 to CIN ground path to prevent switching current spikes from inducing voltage noise into the part. Connect exposed pad to GND plane for optimal thermal performance."}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback. An external resistor divider from the output to GND tapped to the FB pin sets the output voltage. To prevent current limit run away during a short circuit fault condition the frequency-fold-back comparator lowers the oscillator frequency when the FB voltage is below 250mV."}, {"pin_number": "4", "pin_name": "OVP", "pin_description": "Output Over Voltage Protection. Connect OVP to the center point of an external resistor divider from output to GND. The OVP reference is 1.23V."}, {"pin_number": "5", "pin_name": "ISN", "pin_description": "Negative Current Sense Input. It is used for load current limiting and output line drop compensation."}, {"pin_number": "6", "pin_name": "ISP", "pin_description": "Positive Current Sense Input. It is used for load current limiting and output line drop compensation."}, {"pin_number": "7", "pin_name": "BST", "pin_description": "Bootstrap. This capacitor is needed to drive the power switch's gate above the supply voltage. It is connected between SW and BST pins to form a floating supply across the power switch driver. An on-chip regulator is used to charge up the external boot-strap capacitor. If the on-chip regulator is not strong enough, one optional diode can be connected from IN or OUT to charge the external boot-strap capacitor."}, {"pin_number": "8", "pin_name": "SW", "pin_description": "Switch Output. It is the source of power device."}]}], "datasheet_en": {"datasheet_name": "MP2497_r1.12.pdf", "datasheet_path": null, "release_date": "2011-12-22", "version": "1.12"}, "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 50, "min_input_voltage": 4.5, "max_output_voltage": 25, "min_output_voltage": 0.8, "max_output_current": 3, "max_switch_frequency": 0.12, "quiescent_current": 1200, "high_side_mosfet_resistance": 150, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 5, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 2.5, "output_reference_voltage": 0.8, "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "50V", "min_input_voltage": "4.5V", "max_output_voltage": "25V", "min_output_voltage": "0.8V", "max_output_current": "3A", "max_switch_frequency": "100kHz", "quiescent_current": "1200µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "5A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}