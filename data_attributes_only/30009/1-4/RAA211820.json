{"part_number": "RAA211820", "manufacturer": "Renesas", "country": "Japan", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "4.5V to 75V, 2A, DC/DC Synchronous Step-Down Regulator w/ Internal Compensation and Adjustable Frequency", "features": ["Wide input voltage range: 4.5V to 75V", "Adjustable output voltage: 0.8V to 90% of VIN", "Up to 2A of continuous output current", "Default 400kHz switching frequency and programmable switching frequency range from 200kHz to 800kHz", "±1% Load regulation accuracy from -40°C to 125°C, ±0.5% Load regulation accuracy at 25°C", "95µA typical quiescent current", "Internal compensation", "Internal 0.5ms soft-start in QFN", "External programmable soft-start (HTSSOP)", "PFM operation and DEM at light load", "Integrated external bias LDO", "Power good indicator", "Integrated MOSFET rDS(ON) (typical): 155mΩ/80mΩ (QFN); 200mΩ/95mΩ (TSSOP)", "Cycle-by-cycle peak and valley current limit", "Input voltage UVLO and output voltage undervoltage (short-circuit) protection", "Thermal Shutdown", "Available in 20 Ld QFN 4mm x 3.5mm and 16 Ld HTSSOP packages"], "description": "The RAA211820 is a DC/DC synchronous step-down (Buck) regulator that supports a 4.5V to 75V input voltage range and adjustable output voltage. It can deliver up to continuous 2A of continuous output current with premium load and line regulation performance. The RAA211820 uses peak-current mode control architecture. Its PWM switching frequency is programmable to provide the best trade-off between transient response and efficiency. It also supports PFM operation and DEM to maximize light load efficiency, in addition to an external bias LDO input to further reduce power dissipation across the load range. The regulator also has an internal loop compensation circuit to reduce the external component count and BOM cost. The RAA211820 provides useful functions such as internal or programmable soft-start and power good indicator. For safe operation, the RAA211820 also offers protection features such as cycle-by-cycle peak and valley current limit, input voltage UVLO, output voltage undervoltage (short-circuit) protection, and thermal shutdown.", "applications": ["Industrial power systems", "Distributed power supplies and general-purpose point-of-load", "Telecommunication base station power supplies", "High-voltage single-board systems"], "ordering_information": [{"part_number": "RAA211820", "order_device": "RAA211820GNP#HA0", "status": "Active", "package_type": "QFN", "package_code": "L20.3.5x4", "carrier_description": "<PERSON><PERSON>", "carrier_quantity": 6000, "package_drawing_code": "L20.3.5x4", "marking": "RAA2", "pin_count": 20, "length": 3.5, "width": 4.0, "height": 0.9, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": null, "application_grade": "Industrial"}, {"part_number": "RAA211820", "order_device": "RAA211820GSP#HA0", "status": "Active", "package_type": "HTSSOP", "package_code": "M16.173B", "carrier_description": "<PERSON><PERSON>", "carrier_quantity": 2500, "package_drawing_code": "M16.173B", "marking": "11820", "pin_count": 16, "length": 5.0, "width": 4.4, "height": 1.2, "pitch": 0.65, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": null, "application_grade": "Industrial"}], "typical_application_circuit": "https://i.imgur.com/uR1y9l7.png", "pin_config": "https://i.imgur.com/v8t3q4d.png", "function_block_diagram": "https://i.imgur.com/97489Xn.png", "pin_function": [{"product_part_number": "RAA211820", "package_type": "QFN", "pins": [{"pin_number": "1, 13, 14, 15, 16, 17, 18, 19", "pin_name": "PGND", "pin_description": "These pins are connected to the source of the integrated low-side FET and are used as the power ground. Place input bypass capacitors as close as possible to the PGND pins and VIN pin(s)."}, {"pin_number": "2", "pin_name": "SW", "pin_description": "These pins are the phase node of the regulator. They are connected to the source of the high-side FET and the drain of the low-side FET. Connect these pins to the inductor and the boot capacitor."}, {"pin_number": "3, 12, 20", "pin_name": "VIN", "pin_description": "This pin is connected to the drain of the integrated high-side FET. This pin is also connected to the input of internal linear regulator that provides bias for the IC. Connect this pin to the input rail and decouple to PGND with input bypass capacitors."}, {"pin_number": "4", "pin_name": "BST", "pin_description": "This pin is the bootstrap circuit supply pin. Connect this pin to SW with a capacitor to provide bias voltage for the integrated high-side FET gate driver."}, {"pin_number": "5", "pin_name": "EBIAS", "pin_description": "This pin is connected to the auxiliary internal linear regulator. This pin can be connected to the output of the regulator to provide bias for the IC."}, {"pin_number": "6", "pin_name": "FB", "pin_description": "This pin is connected to the inverting input of the feedback error amplifier and should be connected to a properly selected resistor divider from VOUT to ground to set the output voltage."}, {"pin_number": "7", "pin_name": "AGND", "pin_description": "This pin is connected to the analog ground of the IC. Connect this pin to the PCB ground plane."}, {"pin_number": "8", "pin_name": "PG", "pin_description": "This pin is an open-drain power-good output. It can be pulled up with a resistor to VCC or another rail. The recommended pull-up voltage is 3.3V or lower."}, {"pin_number": "9", "pin_name": "FS", "pin_description": "This pin sets the switching frequency. Connect a resistor from this pin to ground to set the switching frequency from 200kHz to 800kHz. Connect this pin to VCC for default 400kHz switching frequency."}, {"pin_number": "10", "pin_name": "VCC", "pin_description": "This pin is connected to the output of internal linear regulators and provides bias for the IC including the gate driver. Connect this pin to ground with a ceramic decoupling capacitor."}, {"pin_number": "11", "pin_name": "EN", "pin_description": "This pin is the enable pin. It is high voltage tolerant and can be directly connected to VIN. When using EN to turn the device on or off, connect this pin to GND with a 1MΩ resistor."}, {"pin_number": "N/A", "pin_name": "PAD", "pin_description": "This is the bottom thermal pad. It should be connected to AGND and to the PCB ground plane with thermal vias."}]}, {"product_part_number": "RAA211820", "package_type": "HTSSOP", "pins": [{"pin_number": "1, 2, 3", "pin_name": "PGND", "pin_description": "These pins are connected to the source of the integrated low-side FET and are used as the power ground. Place input bypass capacitors as close as possible to the PGND pins and VIN pin(s)."}, {"pin_number": "14, 15, 16", "pin_name": "SW", "pin_description": "These pins are the phase node of the regulator. They are connected to the source of the high-side FET and the drain of the low-side FET. Connect these pins to the inductor and the boot capacitor."}, {"pin_number": "4", "pin_name": "VIN", "pin_description": "This pin is connected to the drain of the integrated high-side FET. This pin is also connected to the input of internal linear regulator that provides bias for the IC. Connect this pin to the input rail and decouple to PGND with input bypass capacitors."}, {"pin_number": "13", "pin_name": "BST", "pin_description": "This pin is the bootstrap circuit supply pin. Connect this pin to SW with a capacitor to provide bias voltage for the integrated high-side FET gate driver."}, {"pin_number": "12", "pin_name": "EBIAS", "pin_description": "This pin is connected to the auxiliary internal linear regulator. This pin can be connected to the output of the regulator to provide bias for the IC."}, {"pin_number": "11", "pin_name": "SS", "pin_description": "This pin configures the soft-start time. Connect a capacitor from this pin to ground to program the soft-start time. Tie this pin to VCC for default 0.5ms soft start time."}, {"pin_number": "10", "pin_name": "FB", "pin_description": "This pin is connected to the inverting input of the feedback error amplifier and should be connected to a properly selected resistor divider from VOUT to ground to set the output voltage."}, {"pin_number": "9", "pin_name": "AGND", "pin_description": "This pin is connected to the analog ground of the IC. Connect this pin to the PCB ground plane."}, {"pin_number": "8", "pin_name": "PG", "pin_description": "This pin is an open-drain power-good output. It can be pulled up with a resistor to VCC or another rail. The recommended pull-up voltage is 3.3V or lower."}, {"pin_number": "7", "pin_name": "FS", "pin_description": "This pin sets the switching frequency. Connect a resistor from this pin to ground to set the switching frequency from 200kHz to 800kHz. Connect this pin to VCC for default 400kHz switching frequency."}, {"pin_number": "6", "pin_name": "VCC", "pin_description": "This pin is connected to the output of internal linear regulators and provides bias for the IC including the gate driver. Connect this pin to ground with a ceramic decoupling capacitor."}, {"pin_number": "5", "pin_name": "EN", "pin_description": "This pin is the enable pin. It is high voltage tolerant and can be directly connected to VIN. When using EN to turn the device on or off, connect this pin to GND with a 1MΩ resistor."}, {"pin_number": "-", "pin_name": "PAD", "pin_description": "This is the bottom thermal pad. It should be connected to AGND and to the PCB ground plane with thermal vias."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "RAA211820 Datasheet", "datasheet_path": "用户上传文件", "release_date": "2025-05-22", "version": "Rev.1.02"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 75, "min_input_voltage": 4.5, "max_output_voltage": "90% of VIN", "min_output_voltage": 0.8, "max_output_current": 2, "max_switch_frequency": 0.8, "quiescent_current": 90, "high_side_mosfet_resistance": 155, "low_side_mosfet_resistance": 80, "over_current_protection_threshold": 3.3, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "不适用", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "UVP", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1, "output_reference_voltage": 0.8, "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "75V", "min_input_voltage": "4.5V", "max_output_voltage": "67.5V", "min_output_voltage": "0.8V", "max_output_current": "2A", "max_switch_frequency": "0.8MHz", "quiescent_current": "95µA", "high_side_mosfet_resistance": "155mΩ (QFN), 200mΩ (HTSSOP)", "low_side_mosfet_resistance": "80mΩ (QFN), 95mΩ (HTSSOP)", "over_current_protection_threshold": "3.3A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM/DEM", "power_good_indicator": "Yes", "soft_start": "内部集成 (QFN), 外部可调 (HTSSOP)", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}