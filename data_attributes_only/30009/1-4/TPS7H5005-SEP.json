{"part_number": "TPS7H5005-SEP", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "航天级", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关控制器", "category_lv3": "PWM控制器", "part_number_title": "采用增强型航天塑料的TPS7H500x-SEP 抗辐射 2MHz 电流模式 PWM 控制器", "features": ["耐辐射:", "SEL、SEB 和 SEGR 对于 LET 的抗扰度 = 43MeV-cm²/mg", "SET 和 SEFI的 LET 特征值高达 43MeV-cm²/mg", "每个晶圆批次的保障 TID 高达 50krad(Si)", "输入电压: 4V 至 14V", "在温度、辐射以及线路和负载调节范围内提供 0.613V +0.7%/-1% 的电压基准", "开关频率范围为 100kHz 至 2MHz", "外部时钟同步功能", "同步整流输出", "可调死区时间", "可调前沿消隐时间", "可配置的占空比限值", "可调斜坡补偿和软启动", "24 引脚 TSSOP 封装", "增强型航天塑料"], "description": "TPS7H500x SEP (由 TPS7H5005-SEP、TPS7H5006-SEP、TPS7H5007-SEP 和 TPS7H5008-SEP 组成)是采用航天增强型塑料制成的高速、抗辐射 PWM 控制器系列。这些控制器提供的许多功能有助于设计面向太空应用的直流/直流转换器拓扑。控制器具有 0.613V +0.7%/-1% 的内部精密基准，可配置开关频率高达 2MHz。每个器件都提供可编程斜坡补偿和软启动功能。TPS7H500x-SEP 系列可通过 SYNC 引脚使用外部时钟来驱动，也可使用内部振荡器以用户编程的频率来驱动。此控制器系列为用户提供了各种选项，这些选项用于切换输出、同步整流功能、死区时间（固定或可配置）、前沿消隐时间（固定或可配置）和占空比限制。TPS7H500x-SEP 系列中的每个器件都采用 24 引脚 TSSOP 封装。", "applications": ["用于FPGA、微控制器、数据转换器和ASIC 的太空卫星负载点电源", "通信负载", "命令和数据处理", "光学成像有效载荷", "雷达成像有效载荷", "卫星电力系统"], "ordering_information": [{"part_number": "TPS7H5005-SEP", "order_device": "TPS7H5005MPWTSEP", "status": "Active", "package_type": "TSSOP", "package_code": "PW", "carrier_description": "SMALL T&R", "carrier_quantity": 250, "package_drawing_code": "PW0024A", "marking": "7H5005PW", "pin_count": 24, "length": 7.8, "width": 4.4, "height": 1.2, "pitch": 0.65, "min_operation_temp": -55, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "航天级"}, {"part_number": "TPS7H5005-SEP", "order_device": "V62/22607-01XE", "status": "Active", "package_type": "TSSOP", "package_code": "PW", "carrier_description": "SMALL T&R", "carrier_quantity": 250, "package_drawing_code": "PW0024A", "marking": "7H5005PW", "pin_count": 24, "length": 7.8, "width": 4.4, "height": 1.2, "pitch": 0.65, "min_operation_temp": -55, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "航天级"}], "typical_application_circuit": "https://res.cloudinary.com/dzsrolfjo/image/upload/v1718090001/pvyw57t5f9y66f7f8y6q.png", "pin_config": "https://res.cloudinary.com/dzsrolfjo/image/upload/v1718090002/z6j8q6k0x84q6l6l946i.png", "function_block_diagram": "https://res.cloudinary.com/dzsrolfjo/image/upload/v1718090003/qgqj8z6h936082g6w42i.png", "pin_function": [{"product_part_number": "TPS7H5005-SEP", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "在内部振荡模式下，RT 引脚必须使用一个连接到 AVSS 的电阻器。当 RT 引脚悬空时，SYNC 引脚上需要一个 200kHz 至 4MHz 的外部时钟。外部时钟的频率必须是所需开关频率的两倍。"}, {"pin_number": "2", "pin_name": "PS", "pin_description": "原边关断到同步整流器导通死区时间设置。可通过外部电阻器连接到 AVSS 进行编程。"}, {"pin_number": "3", "pin_name": "SP", "pin_description": "同步整流器关断到原边导通死区时间设置。可通过外部电阻器连接到 AVSS 进行编程。"}, {"pin_number": "4", "pin_name": "LEB", "pin_description": "前沿消隐时间设置。可通过外部电阻器连接到 AVSS 进行编程。"}, {"pin_number": "5", "pin_name": "HICC", "pin_description": "逐周期电流限制时间延迟和打嗝时间设置。延迟时间和打嗝时间由从 HICC 到 AVSS 的电容器决定。将此引脚连接到 AVSS 可禁用打嗝模式。"}, {"pin_number": "6", "pin_name": "SYNC", "pin_description": "当 RT 引脚悬空时，SYNC 配置为 200kHz 至 4MHz 外部时钟的输入。在这种情况下，外部时钟输入被反相，系统时钟将以外部时钟输入频率的一半运行。当 RT 引脚连接电阻到 AVSS 时，SYNC 输出一个 200kHz 至 4MHz 的时钟信号，频率是器件开关频率的两倍，与器件的开关同相。"}, {"pin_number": "7", "pin_name": "DCL", "pin_description": "占空比限制可配置性。对于 TPS7H5005-SEP，连接到 AVSS 表示 50% 占空比限制，悬空表示 75%，连接到 VLDO 表示 100%。"}, {"pin_number": "8", "pin_name": "EN", "pin_description": "将 EN 引脚连接到 VLDO 引脚或大于 0.6V 的外部源可启用器件。此外，可以使用两个电阻器调整输入欠压锁定 (UVLO)。"}, {"pin_number": "9", "pin_name": "VIN", "pin_description": "器件的输入电源。输入电压范围为 4V 至 14V。"}, {"pin_number": "10", "pin_name": "OUTA", "pin_description": "原边开关输出 A。"}, {"pin_number": "11", "pin_name": "OUTB", "pin_description": "原边开关输出 B。仅当 DCL = AVSS 时有效。"}, {"pin_number": "12, 13", "pin_name": "NC", "pin_description": "无连接。如果需要，可以连接到 AVSS 以避免浮空金属。"}, {"pin_number": "14", "pin_name": "SRB", "pin_description": "同步整流器输出 B。仅当 DCL = AVSS 时有效。"}, {"pin_number": "15", "pin_name": "SRA", "pin_description": "同步整流器输出 A。"}, {"pin_number": "16", "pin_name": "AVSS", "pin_description": "器件的接地。"}, {"pin_number": "17", "pin_name": "VLDO", "pin_description": "内部稳压器的输出。需要至少一个 1µF 的外部电容器连接到 AVSS。"}, {"pin_number": "18", "pin_name": "CS_ILIM", "pin_description": "用于 PWM 控制和逐周期过流保护的电流检测。CS_ILIM 上的输入电压超过 1.05V 将在 PWM 控制器中触发过流。与 PWM 比较器输入的 COMP/2 电压相比，CS_ILIM 上的检测波形包含 150mV 的偏移。"}, {"pin_number": "19", "pin_name": "FAULT", "pin_description": "故障保护引脚。当 FAULT 引脚的上升阈值被超过时，输出将停止开关。在外部电压降至下降阈值以下后，器件将在设定的延迟后重新启动。将此引脚连接到 AVSS 可禁用 FAULT。"}, {"pin_number": "20", "pin_name": "REFCAP", "pin_description": "1.2V 内部基准。需要一个 470nF 的外部电容器连接到 AVSS。"}, {"pin_number": "21", "pin_name": "RSC", "pin_description": "从 RSC 到 AVSS 的电阻器设置所需的斜坡补偿。"}, {"pin_number": "22", "pin_name": "SS", "pin_description": "软启动。连接到此引脚的外部电容器设置内部电压基准的上升时间。此引脚上的电压会覆盖内部基准。它可用于跟踪和排序。"}, {"pin_number": "23", "pin_name": "VSENSE", "pin_description": "误差放大器的反相输入。"}, {"pin_number": "24", "pin_name": "COMP", "pin_description": "误差放大器输出。将频率补偿连接到此引脚。"}]}], "datasheet_cn": {"datasheet_name": "ZHCSOF4A", "datasheet_path": "未找到", "release_date": "2022-09", "version": "A"}, "datasheet_en": {"datasheet_name": "SLVSGG1", "datasheet_path": "未找到", "release_date": "未找到", "version": "未找到"}, "family_comparison": "https://res.cloudinary.com/dzsrolfjo/image/upload/v1718090004/z3z6j1h96082g6w42i.png", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET/氮化镓(GAN)", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "14V", "min_input_voltage": "4V", "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": "不适用", "max_switch_frequency": "2MHz", "quiescent_current": "9mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "1.05V", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "脉冲跳跃 (Pulse Skipping)", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Latch", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.613V", "loop_control_mode": "峰值电流模式"}}