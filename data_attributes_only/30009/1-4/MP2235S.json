{"part_number": "MP2235S", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压芯片", "part_number_title": "High-Efficiency, 3 A, 16 V, 800 kHz Synchronous Step-Down Converter", "features": ["Wide 4.5 V to 16 V Operating Input Range", "120 mΩ/50 mΩ Low RDS(ON) Internal Power MOSFETS", "High-Efficiency Synchronous Mode Operation", "Fixed 800 kHz Switching Frequency", "Synchronizes from a 300 kHz to a 2 MHz External Clock", "Power-Save Mode at Light Load", "External Soft-Start", "Over-Current Protection and Hiccup", "Thermal Shutdown", "Output Adjustable from 0.804 V", "Available in a 8-pin TSOT-23 Package"], "description": "The MP2235S is a high-frequency, synchronous, rectified, step-down, switch-mode converter with built-in power MOSFETs. It offers a compact solution to achieve a 3 A continuous output current with excellent load and line regulation over a wide input supply range. The MP2235S has synchronous mode operation for higher efficiency over the output current load range.\nCurrent mode operation provides fast transient response and eases loop stabilization.\nFull protection features include over-current protection (OCP) and thermal shutdown (TSD).\nThe MP2235S requires a minimal number of readily available, standard, external components and is available in a space-saving 8-pin TSOT23 package.", "applications": ["Notebook Systems and I/O Power", "Digital Set-Top Boxes", "Flat-Panel Televisions and Monitors", "Distributed Power Systems"], "ordering_information": [{"part_number": "MP2235S", "order_device": "MP2235SGJ", "status": "Active", "package_type": "TSOT23-8", "package_code": "TSOT23-8", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MO-193, VARIATION BA", "marking": "AQA", "pin_count": 8, "length": 2.9, "width": 2.8, "height": 1.0, "pitch": 0.65, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "MP2235S", "package_type": "TSOT23-8", "pins": [{"pin_number": "1", "pin_name": "SS", "pin_description": "Soft start. Connect an external capacitor to program the soft-start time for the switch mode regulator."}, {"pin_number": "2", "pin_name": "IN", "pin_description": "Supply voltage. IN supplies power for the internal MOSFET and regulator. The MP2235S operates from a +4.5 V to +16 V input rail. IN requires a low ESR and low-inductance capacitor (C1) to decouple the input rail. Place the input capacitor very close to IN and connect it with wide PCB traces and multiple vias."}, {"pin_number": "3", "pin_name": "SW", "pin_description": "Switch output. Connect SW to the inductor and bootstrap capacitor. SW is driven up to VIN by the high-side switch during the PWM duty cycle on time. The inductor current drives SW negative during the off time. The on resistance of the low-side switch and the internal body diode fixes the negative voltage. Connect SW using wide PCB traces and multiple vias."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "System ground. GND is the reference ground of the regulated output voltage. PCB layout requires extra care. For best results, connect to GND with copper and vias."}, {"pin_number": "5", "pin_name": "BST", "pin_description": "Bootstrap. Requires a capacitor connected between SW and BST to form a floating supply across the high-side switch driver."}, {"pin_number": "6", "pin_name": "EN/SYNC", "pin_description": "Enable. EN=high to enable the MP2235S. Apply an external clock to change the switching frequency. For automatic start-up, connect EN to Vin with a 100 kΩ resistor."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "Internal 5 V LDO output. VCC powers the driver and control circuits. Decouple with a 0.1 μF to 0.22 µF capacitor. Do NOT use a capacitor ≥0.22 µF."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Feedback. Connect FB to the tap of an external resistor divider from the output to GND to set the output voltage. The frequency foldback comparator lowers the oscillator frequency when the FB voltage is below 400 mV to prevent current limit runaway during a short-circuit fault. Place the resistor divider as close to FB as possible. Avoid placing vias on the FB traces."}]}], "datasheet_en": {"datasheet_name": "MP2235S.pdf", "datasheet_path": "Not applicable", "release_date": "2015-04-15", "version": "1.0"}, "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "4.5V", "max_output_voltage": "14.72V", "min_output_voltage": "0.804V", "max_output_current": "3A", "max_switch_frequency": "2MHz", "quiescent_current": "500µA", "high_side_mosfet_resistance": "120mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "No", "enable_function": "Yes", "light_load_mode": "AAM (PFM)", "power_good_indicator": "No", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "0.804V", "loop_control_mode": "峰值电流模式"}}