{"part_number": "TPS546C23", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "工业级", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片(<PERSON>)", "part_number_title": "TPS546C23 具有 PMBus 的 4.5V 至 18V、35A 可堆叠同步降压转换器", "features": ["符合 PMBus™1.3 规范的转换器：35A", "两器件进行堆叠可实现高达 70A 的电流，同时具备分流功能", "输入电压范围：4.5V 至 18V", "输出电压范围：0.35V 至 5.5V", "5mm × 7mm LQFN 封装", "单个散热焊盘", "集成 3.2mΩ 和 1.4mΩ 堆叠 NexFET™ 功率级", "适用于自适应电压定标 (AVS) 功能和通过 PMBus 进行调整的基准电压为 350mV 至 1650mV", "电压不低于 600mV 时的精度为 0.5%", "无损低侧金属氧化物半导体场效应晶体管 (MOSFET) 电流感测", "带有输入前馈的电压模式控制", "差分远程感应", "单启动至预偏置输出", "输出电压和输出电流报告", "内部芯片温度监控", "可通过 ADDR0 和 ADDR1 引脚编程设定 64 种 PMBus 地址", "可通过 PMBus 接口进行编程", "热关断", "引脚配置适用的开关频率：200kHz 至 1MHz", "与外部时钟或同步输出的输出时钟频率同步"], "description": "TPS546C23 器件是采用 5mm × 7mm 封装且符合 PMBus 1.3 规范的非隔离式 DC-DC 转换器，集成了 FET，能够在高频下运行并输出 35A 电流。两个 TPS546C23 器件可以并联，以便产生高达 70A 的负载电流。通过针对少量功率级电流进行采样实现电流感测，与器件温度无关。集成 NexFET 功率级和优化驱动器提供高频低损耗开关功能，可实现超高密度电源解决方案。PMBus 接口通过 VOUT_COMMAND 启用 AVS 功能，同时支持灵活转换器配置以及关键参数监控功能（包括输出电压、电流和内部芯片温度监控）。对故障条件的响应可设为重启、锁存或忽略，具体取决于系统要求。", "applications": ["测试和测量仪器", "以太网交换机、光交换机、路由器、基站", "服务器", "企业级存储固态硬盘 (SSD)", "高密度电源解决方案"], "ordering_information": [{"part_number": "TPS546C23", "order_device": "TPS546C23RVFR", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": "2500", "package_drawing_code": "RVF", "marking": "TPS546C23", "pin_count": "40", "length": "7.0", "width": "5.0", "height": "1.52", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C23", "order_device": "TPS546C23RVFR.A", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": "2500", "package_drawing_code": "RVF", "marking": "TPS546C23", "pin_count": "40", "length": "7.0", "width": "5.0", "height": "1.52", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C23", "order_device": "TPS546C23RVFR.B", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": "2500", "package_drawing_code": "RVF", "marking": "TPS546C23", "pin_count": "40", "length": "7.0", "width": "5.0", "height": "1.52", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C23", "order_device": "TPS546C23RVFT", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RVF", "marking": "TPS546C23", "pin_count": "40", "length": "7.0", "width": "5.0", "height": "1.52", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C23", "order_device": "TPS546C23RVFT.A", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RVF", "marking": "TPS546C23", "pin_count": "40", "length": "7.0", "width": "5.0", "height": "1.52", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C23", "order_device": "TPS546C23RVFT.B", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RVF", "marking": "TPS546C23", "pin_count": "40", "length": "7.0", "width": "5.0", "height": "1.52", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C23", "order_device": "TPS546C23RVFTG4.A", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RVF", "marking": "TPS546C23", "pin_count": "40", "length": "7.0", "width": "5.0", "height": "1.52", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS546C23", "order_device": "TPS546C23RVFTG4.B", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RVF", "marking": "TPS546C23", "pin_count": "40", "length": "7.0", "width": "5.0", "height": "1.52", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS546C23", "package_type": "LQFN-40", "pins": [{"pin_number": "1", "pin_name": "RT", "pin_description": "频率设定电阻器。将一个电阻器从此引脚连接至 AGND 以对开关频率进行编程。请勿使此引脚悬空。"}, {"pin_number": "2", "pin_name": "ADDR1", "pin_description": "设定 PMBus 地址的高位 3 位。将一个电阻器连接在此引脚和 AGND 之间。"}, {"pin_number": "3", "pin_name": "ADDR0", "pin_description": "设定 PMBus 地址的低位 3 位。将一个电阻器连接在此引脚和 AGND 之间。"}, {"pin_number": "4", "pin_name": "PMB_DATA", "pin_description": "PMBus DATA 引脚。请参阅 Supported PMBus Commands 部分。"}, {"pin_number": "5", "pin_name": "PMB_CLK", "pin_description": "PMBus CLK 引脚。请参阅 Supported PMBus Commands 部分。"}, {"pin_number": "6", "pin_name": "SMB_ALRT", "pin_description": "SMBus™ 警报引脚。请参阅 Supported PMBus Commands 部分。"}, {"pin_number": "7", "pin_name": "BOOT", "pin_description": "用于内部浮动高侧驱动器的自举引脚。将一个 100nF（典型值）电容器从此引脚连接至 SW 引脚。为了减小 SW 上的电压尖峰，可在 BOOT 电容器上串联一个值为 1Ω 至 15Ω 的 BOOT 电阻器，以减缓高侧 FET 的导通速度。"}, {"pin_number": "8, 9, 10, 11, 12", "pin_name": "SW", "pin_description": "器件的开关电源输出。将输出平均滤波器和自举电容器连接至该组引脚。"}, {"pin_number": "13, 14, 15, 16, 17, 18, 19, 20", "pin_name": "PGND", "pin_description": "功率级接地返回。这些引脚在内部连接至散热焊盘。"}, {"pin_number": "21, 22, 23, 24, 25", "pin_name": "PVIN", "pin_description": "功率级的输入电源。这些引脚至 PGND 的低阻抗旁路至关重要。"}, {"pin_number": "26", "pin_name": "DRGND", "pin_description": "控制器件的功率接地返回。此引脚应直接连接至 PCB 板上的散热焊盘。"}, {"pin_number": "27", "pin_name": "BP3", "pin_description": "3.3V 板载稳压器的输出。此稳压器为控制器供电，应使用一个最小值为 2.2µF 的电容器旁路至 AGND。BP3 引脚不适用于为外部电路供电。"}, {"pin_number": "28", "pin_name": "BP6", "pin_description": "6.5V 板载稳压器的输出。此稳压器为控制器的驱动级供电，应使用一个最小值为 2.2µF 的电容器旁路至散热焊盘（功率级接地，基本上是 PGND）。TI 建议使用一个额外的 100nF（典型值）旁路电容器来减小 BP6 上的纹波。此引脚至 PGND 的低阻抗旁路至关重要。"}, {"pin_number": "29", "pin_name": "AVIN", "pin_description": "控制器的输入电源。使用一个最小值为 1µF 的低阻抗旁路电容器连接至 PGND。AVIN 电压也用于输入前馈。PVIN 和 AVIN 必须处于相同电位，以实现精确的短路保护。"}, {"pin_number": "30", "pin_name": "RESET/PGD", "pin_description": "此引脚用于输出电压复位或电源正常输出。此引脚的功能由用户可访问的位 EN_RESET_B 在 MFR_SPECIFIC_21 (E4h) 寄存器中确定。此引脚的默认功能是电源正常指示器。对于输出电压复位，此引脚是逻辑低电平输入。存在一个 750kΩ 的内部下拉电阻，因此该引脚需要一个上拉电阻来启用 VOUT 的编程。作为电源正常指示器，此引脚是一个开漏输出，当器件工作且处于稳压状态时，该输出会浮动至外部上拉。在任何故障或警告条件下，此引脚被拉低。有关详细信息，请参见表 2。不使用时，PGD 引脚可保持悬空。"}, {"pin_number": "31", "pin_name": "ISHARE", "pin_description": "用于 2 相操作的电流共享信号。对于独立器件，ISHARE 引脚可保持悬空。"}, {"pin_number": "32", "pin_name": "VSHARE", "pin_description": "用于 2 相操作的电压共享信号。对于独立器件，VSHARE 引脚可保持悬空。"}, {"pin_number": "33", "pin_name": "RSP", "pin_description": "远程感应放大器的正输入。对于独立器件或 2 相配置中的环路主器件，将 RSP 引脚连接至负载处的输出电压。对于 2 相配置中的环路从器件，远程感应放大器不用于输出电压感应或调节。"}, {"pin_number": "34", "pin_name": "RSN", "pin_description": "远程感应放大器的负输入。对于独立器件或 2 相配置中的环路主器件，将 RSN 引脚连接至负载处的接地。对于 2 相配置中的环路从器件，远程感应放大器不用于输出电压感应或调节。"}, {"pin_number": "35", "pin_name": "DIFFO", "pin_description": "差分远程感应放大器的输出。这为输出电压报告和电压控制环路提供远程感应。对于 2 相配置中的环路从器件，DIFFO 引脚可保持悬空。"}, {"pin_number": "36", "pin_name": "FB", "pin_description": "控制环路的反馈引脚。误差放大器的负输入。在 2 相配置中，环路从器件的 FB 引脚应连接至 BP3 引脚。"}, {"pin_number": "37", "pin_name": "COMP", "pin_description": "误差放大器的输出。将补偿网络从此引脚连接至 FB 引脚。"}, {"pin_number": "38", "pin_name": "AGND", "pin_description": "控制器件的模拟接地返回。将此引脚连接至散热焊盘上的 PGND 和 DRGND。"}, {"pin_number": "39", "pin_name": "SYNC", "pin_description": "用于频率同步。对于独立器件或 2 相配置中的环路主器件，通过外部上拉至 BP6 引脚，SYNC 引脚将配置为 SYNC-IN 引脚，并将与施加于此引脚的外部时钟的上升沿同步。否则，SYNC 引脚将配置为 SYNC-OUT 引脚。对于 2 相配置中的环路从器件，SYNC 引脚始终为 SYNC-IN，并将与 SYNC 引脚上输入时钟的下降沿同步。只有 50% 占空比的外部时钟可以应用于 2 相堆叠以实现两相的交错。将外部时钟应用于环路主器件和环路从器件以同步堆叠是可选的。没有外部时钟时，环路主器件将向环路从器件输出一个 50% 占空比的时钟，从器件将与该时钟的下降沿同步。不使用时，SYNC 引脚可保持悬空。"}, {"pin_number": "40", "pin_name": "CNTL", "pin_description": "PMBus CNTL 引脚。请参阅 Supported PMBus Commands 部分。CNTL 引脚具有内部上拉，悬空时为高电平。"}, {"pin_number": "Thermal pad", "pin_name": "Thermal pad", "pin_description": "封装散热焊盘，内部连接至 PGND。散热焊盘必须有足够的焊料覆盖以确保正常工作。"}]}], "datasheet_cn": {"name": "TPS546C23 ZHCSFG4B-JULY 2016-REVISED NOVEMBER 2016", "path": "TPS546C23_ZHCSFG4B.pdf", "release_date": "2016-11", "version": "ZHCSFG4B"}, "datasheet_en": {"name": "SLUSCC7", "path": "未找到", "release_date": "未找到", "version": "未找到"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "18", "min_input_voltage": "4.5", "max_output_voltage": "5.5", "min_output_voltage": "0.35", "max_output_current": "35", "max_switch_frequency": "1", "quiescent_current": "7700", "high_side_mosfet_resistance": "3.2", "low_side_mosfet_resistance": "1.4", "over_current_protection_threshold": "42", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "0.5", "output_reference_voltage": "0.6", "loop_control_mode": "电压模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "4.5V", "max_output_voltage": "5.5V", "min_output_voltage": "0.35V", "max_output_current": "35A", "max_switch_frequency": "1MHz", "quiescent_current": 7700, "high_side_mosfet_resistance": "3.2mΩ", "low_side_mosfet_resistance": "1.4mΩ", "over_current_protection_threshold": "5-52A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "CCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Latch", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": 0.5, "output_reference_voltage": 0.6, "loop_control_mode": "电压模式"}}