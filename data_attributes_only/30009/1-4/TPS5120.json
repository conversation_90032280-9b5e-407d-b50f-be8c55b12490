{"part_number": "TPS5120", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压型控制器（外部开关）", "part_number_title": "DUAL OUTPUT, TWO-<PERSON><PERSON><PERSON> SYNCHRONOUS BUCK DC/DC CONTROLLER", "features": ["Independent Dual-Outputs Operate 180° Out of Phase", "Wide Input Voltage Range: 4.5-V – 28-V", "Adjustable Output Voltage Down to 0.9 V", "Pin-Selectable PWM/SKIP Mode for High Efficiency Under Light Loads", "Synchronous Buck Operation Allows up to 95% Efficiency", "Separate Standby Control and Overcurrent Protection for Each Channel", "Programmable Short-Circuit Protection", "Low Supply (1 mA) and Shutdown (1 nA) Current", "Power Good Output", "High-Speed Error Amplifiers", "Sequencing Easily Achieved by Selecting Softstart Capacitor Values.", "5-V Linear Regulator Power Internal IC Circuitry", "30-Pin TSSOP Packaging"], "description": "The TPS5120 is a dual channel, high-efficiency synchronous buck controller where the outputs run 180 degrees out of phase, which lowers the input current ripple, thereby reducing the input capacitance cost. The PWM/SKIP pin allows the operating mode to switch from PWM mode to skip mode under light load conditions. The skip mode enables a lower operating frequency and shortens the pulse width to the low-side MOSFET, increasing the efficiency under light load conditions. These two modes, along with synchronous-rectifier drivers, dead time, and very low quiescent current, allow power to be conserved and the battery life to be extended under all load conditions. The 1.5 A (typical) high-side and low-side MOSFET drivers on-chip are designed to drive less expensive N-channel MOSFETs. The resistorless current protection and fixed high-side driver voltage simplify the power supply design and reduce the external parts count. Each channel is independent, offering a separate controller, overcurrent protection, and standby control. Sequencing is flexible and can be tailored by choosing different softstart capacitor values. Other features, such as undervoltage lockout, power good, overvoltage, undervoltage, and programmable short-circuit protection promote system reliability.", "applications": "未找到", "ordering_information": [{"part_number": "TPS5120", "order_device": "TPS5120DBT", "status": "Active", "package_type": "TSSOP", "package_code": "DBT", "carrier_description": "TUBE", "carrier_quantity": "60", "package_drawing_code": "DBT0030A", "marking": "PS5120", "pin_count": "30", "length": null, "width": null, "height": "1.2", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "Adjustable", "application_grade": "Industrial"}, {"part_number": "TPS5120", "order_device": "TPS5120DBT.A", "status": "Active", "package_type": "TSSOP", "package_code": "DBT", "carrier_description": "TUBE", "carrier_quantity": "60", "package_drawing_code": "DBT0030A", "marking": "PS5120", "pin_count": "30", "length": null, "width": null, "height": "1.2", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "Adjustable", "application_grade": "Industrial"}, {"part_number": "TPS5120", "order_device": "TPS5120DBTR", "status": "Active", "package_type": "TSSOP", "package_code": "DBT", "carrier_description": "LARGE T&R", "carrier_quantity": "2000", "package_drawing_code": "DBT0030A", "marking": "PS5120", "pin_count": "30", "length": null, "width": null, "height": "1.2", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "Adjustable", "application_grade": "Industrial"}, {"part_number": "TPS5120", "order_device": "TPS5120DBTR.A", "status": "Active", "package_type": "TSSOP", "package_code": "DBT", "carrier_description": "LARGE T&R", "carrier_quantity": "2000", "package_drawing_code": "DBT0030A", "marking": "PS5120", "pin_count": "30", "length": null, "width": null, "height": "1.2", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "Adjustable", "application_grade": "Industrial"}, {"part_number": "TPS5120", "order_device": "TPS5120DBTRG4", "status": "Active", "package_type": "TSSOP", "package_code": "DBT", "carrier_description": "LARGE T&R", "carrier_quantity": "2000", "package_drawing_code": "DBT0030A", "marking": "PS5120", "pin_count": "30", "length": null, "width": null, "height": "1.2", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "TPS5120", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "INV1", "pin_description": "Inverting input of the CH1 error amplifier, skip comparator, and OVP1/UVP1 comparator"}, {"pin_number": "2", "pin_name": "FB1", "pin_description": "Feedback output of CH1 error amplifier"}, {"pin_number": "3", "pin_name": "SOFTSTART1", "pin_description": "External capacitor from SOFTSTART1 to GND for CH1 softstart control. Separate soft-start terminals make it possible to set the start-up time of each output independently."}, {"pin_number": "4", "pin_name": "PWM/SKIP", "pin_description": "PWM/SKIP mode select pin. The PWM/SKIP pin is used to change the output's operating mode. If this terminal is lower than 0.5 V, it works in PWM mode. When a minimum voltage of 2 V is applied, the device operates in skip mode. In light load condition (< 0.2 A), the skip mode gives a short pulse to the low-side FETs instead of a full pulse. With this control, switching frequency is lowered and switching loss is reduced. Also, the output capacitor energy discharging through the output inductor and low-side FETs is stopped. Therefore, TPS5120 achieves a higher efficiency in light load conditions."}, {"pin_number": "5", "pin_name": "CT", "pin_description": "External capacitor from CT to GND for adjusting the triangle oscillator"}, {"pin_number": "6", "pin_name": "5V_STBY", "pin_description": "5-V linear regulator control"}, {"pin_number": "7", "pin_name": "GND", "pin_description": "Control GND"}, {"pin_number": "8", "pin_name": "REF", "pin_description": "0.85-V reference voltage output. The 0.85-V reference voltage is used for setting the output voltage and the voltage protection. This reference voltage is dropped down from a 5-V regulator."}, {"pin_number": "9", "pin_name": "STBY1", "pin_description": "Standby control for CH1. SMPS1 can be switched into standby mode separately by grounding the STBY1 pin."}, {"pin_number": "10", "pin_name": "STBY2", "pin_description": "Standby control for CH2. SMPS2 can be switched into standby mode separately by grounding the STBY2 pin."}, {"pin_number": "11", "pin_name": "FLT", "pin_description": "Fault latch timer pin. An external capacitor is connected between FLT and GND to set the FLT enable time up."}, {"pin_number": "12", "pin_name": "POWERGOOD", "pin_description": "Power good open-drain output. When low, POWERGOOD reports an output fail condition. PG comparators monitor both SMPS's over voltage and UVLO of VREF5. The threshold is ±7%. When the SMPS starts up, the POWERGOOD pin's output goes high. POWERGOOD also monitors VREF5's UVLO output."}, {"pin_number": "13", "pin_name": "SOFTSTART2", "pin_description": "External capacitor from SOFTSTART2 to GND for CH2 softstart control. Separate soft-start terminals make it possible to set the start-up time of each output independently."}, {"pin_number": "14", "pin_name": "FB2", "pin_description": "Feedback output of CH2 error amplifier"}, {"pin_number": "15", "pin_name": "INV2", "pin_description": "Inverting input of the CH2 error amplifier, skip comparator, and OVP2/UVP2 comparator"}, {"pin_number": "16", "pin_name": "LH2", "pin_description": "Bootstrap capacitor connection for CH2 high-side gate drive"}, {"pin_number": "17", "pin_name": "OUT2_u", "pin_description": "Gate drive output for CH2 high-side switching FETs"}, {"pin_number": "18", "pin_name": "LL2", "pin_description": "Bootstrap this pin low for CH2 high-side gate driving return and output current protection. Connect this pin to the junction of the high-side and low-side FETs for a floating drive configuration."}, {"pin_number": "19", "pin_name": "OUT2_d", "pin_description": "Gate drive output for CH2 low-side gate drive"}, {"pin_number": "20", "pin_name": "OUTGND2", "pin_description": "Ground for CH2 FET drivers"}, {"pin_number": "21", "pin_name": "REG5V_IN", "pin_description": "External 5-V input"}, {"pin_number": "22", "pin_name": "VREF5", "pin_description": "5-V internal regulator output"}, {"pin_number": "23", "pin_name": "TRIP2", "pin_description": "External resistor connection for CH2 output current control"}, {"pin_number": "24", "pin_name": "Vcc", "pin_description": "Supply voltage input"}, {"pin_number": "25", "pin_name": "TRIP1", "pin_description": "External resistor connection for CH1 output current control"}, {"pin_number": "26", "pin_name": "OUTGND1", "pin_description": "Ground for CH1 FET drivers"}, {"pin_number": "27", "pin_name": "OUT1_d", "pin_description": "Gate drive output for CH1 low-side gate drive"}, {"pin_number": "28", "pin_name": "LL1", "pin_description": "Bootstrap this pin low for CH1 high-side gate driving return and output current protection. Connect this pin to the junction of the high-side and low-side FETs for a floating drive configuration."}, {"pin_number": "29", "pin_name": "OUT1_u", "pin_description": "Gate drive output for CH1 high-side switching FETs"}, {"pin_number": "30", "pin_name": "LH1", "pin_description": "Bootstrap capacitor connection for CH1 high-side gate drive"}]}], "datasheet_en": {"name": "TPS5120", "path": "path/to/TPS5120.pdf", "issue_date": "2003-03-01", "version": "SLVS278E"}, "family_comparison": "未找到", "power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": 28, "min_input_voltage": 4.5, "max_output_voltage": "未找到", "min_output_voltage": 0.9, "max_output_current": "Dependent on external components", "max_switch_frequency": 0.5, "quiescent_current": 1100, "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "No", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1, "output_reference_voltage": 0.85, "loop_control_mode": "Voltage Mode", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 2, "channel_count": 2, "max_input_voltage": "28V", "min_input_voltage": "4.5V", "max_output_voltage": "未找到", "min_output_voltage": "0.9V", "max_output_current": "由外部元件决定", "max_switch_frequency": "500kHz", "quiescent_current": "1.1mA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PWM/SKIP", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.85V", "loop_control_mode": "电压模式"}}