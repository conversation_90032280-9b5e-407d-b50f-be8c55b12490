{"part_number": "TPS51427A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "工业级", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关电源芯片", "category_lv3": "DC-DC开关控制器", "part_number_title": "DUAL D-CAPTM SYNCHRONOUS STEP-DOWN CONTROLLER FOR NOTEBOOK POWER RAILS", "features": ["Fixed-Frequency Emulated On-Time Control; Frequency Selectable from Three Options", "D-CAPTM Mode Enables Fast Transient Response Less than 100 ns", "Advanced Ramp Compensation Allows Low Output Ripple with Minimal Jitter", "Selectable PWM-Only/OOA™/Auto-Skip Modes", "Wide Input Voltage Range: 5.5 V to 28 V", "Dual Fixed or Adjustable SMPS: 0.7 V to 5.9 V (Channel1), 0.5 V to 2.5 V (Channel2)", "Fixed 3.3-V/5-V, or Adjustable Output 0.7-V to 4.5-V LDO; Capable of Sourcing 100 mA", "Fixed 3.3-VREF Output Capable of Sourcing 10 mA", "Temperature Compensated Low-Side RDS(on) Current Sensing", "Adaptive Gate Drivers with Integrated Boost Switch", "Bootstrap Charge Auto Refresh", "Integrated Soft Start, Tracking Soft Stop", "Independent PGOOD and EN for Each Channel", "OOB Function Disabled. Refer to TPS51427 for OOB-Enabled Device"], "description": "The TPS51427A is a dual synchronous step-down controller designed for notebook and mobile communications applications. This device is part of a low-cost suite of notebook power bus regulators that enables system designs with low external component counts. The TPS51427A includes two pulse-width-modulation (PWM) controllers, SMPS1 and SMPS2. The output of SMPS1 can be adjusted from 0.7 V to 5.9 V, while the output of SMPS2 can be adjusted from 0.5 V to 2.5 V. This device also features a low-dropout (LDO) regulator that provides a 5-V/3.3-V output, or adjustable from 0.7-V to 4.5-V output via LDOREFIN. The fixed-frequency emulated adaptive on-time control supports seamless operation between PWM mode under heavy load conditions and reduced frequency operation at light loads for high-efficiency down to the milliampere range. An integrated boost switch enhances the high-side MOSFET to further improve efficiency. The main control loop is the D-CAP™ mode that is optimized for low equivalent series resistance (ESR) output capacitors such as POSCAP or SP-CAP. Advanced ramp compensation minimizes jitter without degrading line and load regulation. RDS(on) current sensing methods offers maximum cost saving. The TPS51427A supports supply input voltages that range from 5.5 V to 28 V. It is available in the 32-pin, 5-mm x 5-mm QFN package (Green, RoHs-compliant, and Pb-free). The device is specified from -40°C to +85°C.", "applications": ["Notebook I/O and System Bus Rails", "Graphics Application", "PDAs and Mobile Communication Devices"], "ordering_information": [{"part_number": "TPS51427A", "order_device": "TPS51427ARHBR", "status": "Active", "package_type": "VQFN", "package_code": "RHB", "carrier_description": "Large Tape and Reel", "carrier_quantity": "3000", "package_drawing_code": "RHB", "marking": "TPS 51427A", "pin_count": "32", "length": "5", "width": "5", "height": "1", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "未找到", "application_grade": "工业级"}, {"part_number": "TPS51427A", "order_device": "TPS51427ARHBT", "status": "Active", "package_type": "VQFN", "package_code": "RHB", "carrier_description": "Small Tape and Reel", "carrier_quantity": "250", "package_drawing_code": "RHB", "marking": "TPS 51427A", "pin_count": "32", "length": "5", "width": "5", "height": "1", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "未找到", "application_grade": "工业级"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS51427A", "package_type": "QFN", "pins": [{"pin_number": "15", "pin_name": "DRVH1", "pin_description": "High-side N-Channel FET driver outputs. LL referenced floating drivers. The gate drive voltage is defined by the voltage across VBST to LL node bootstrap capacitor."}, {"pin_number": "26", "pin_name": "DRVH2", "pin_description": "High-side N-Channel FET driver outputs. LL referenced floating drivers. The gate drive voltage is defined by the voltage across VBST to LL node bootstrap capacitor."}, {"pin_number": "18", "pin_name": "DRVL1", "pin_description": "Synchronous low-side MOSFET driver outputs. Ground referenced drivers. The gate drive voltage is defined by V5DRV voltage."}, {"pin_number": "23", "pin_name": "DRVL2", "pin_description": "Synchronous low-side MOSFET driver outputs. Ground referenced drivers. The gate drive voltage is defined by V5DRV voltage."}, {"pin_number": "14", "pin_name": "EN1", "pin_description": "Channel enable pins. If EN1 is connected to VREF2, Channel1 starts after Channel2 reaches regulation (delay start). If EN2 is connected to VREF2, Channel2 starts after Chanel1 reaches regulation."}, {"pin_number": "27", "pin_name": "EN2", "pin_description": "Channel enable pins. If EN1 is connected to VREF2, Channel1 starts after Channel2 reaches regulation (delay start). If EN2 is connected to VREF2, Channel2 starts after Chanel1 reaches regulation."}, {"pin_number": "4", "pin_name": "EN_LDO", "pin_description": "LDO Enable Input. The LDO is enabled if EN_LDO is within logic high level and disabled if EN_LDO is less than the logic low level."}, {"pin_number": "21", "pin_name": "GND", "pin_description": "Analog ground for both channels and LDO."}, {"pin_number": "16", "pin_name": "LL1", "pin_description": "Phase node connections for high-side drivers. These connections also serve as inputs to current comparators for RDS(on) sensing and input voltage monitor for on-time control circuitry."}, {"pin_number": "25", "pin_name": "LL2", "pin_description": "Phase node connections for high-side drivers. These connections also serve as inputs to current comparators for RDS(on) sensing and input voltage monitor for on-time control circuitry."}, {"pin_number": "7", "pin_name": "LDO", "pin_description": "Linear regulator output. The LDO regulator can provide a total of 100-mA external loads. The LDO regulates at 5 V If LDOREFIN is connected to GND. When the LDO is set at 5 V and VSW is within a 5-V switchover threshold, the internal regulator shuts down and the LDO output pin connects to VSW through a 0.7-Ω switch. The LDO regulates at 3.3 V if LDOREFIN is connected to V5FILT. When the LDO is set at 3.3 V and VSW is within a 3.3-V switchover threshold, the internal regulator shuts down and the LDO output pin connects to VSW through a 0.7-Ω switch. Bypass the LDO output with a minimum of 4.7-µF ceramic capacitance."}, {"pin_number": "8", "pin_name": "LDOREFIN", "pin_description": "LDO Reference Input. Connect LDOREFIN to GND for fixed 5-V operation. Connect LDOREFIN to V5FILT for fixed 3.3-V operation. LDOREFIN can be used to program LDO output voltage from 0.7 V to 4.5 V. LDO output is twice the voltage of LDOREFIN. There is no switchover in adjustable mode."}, {"pin_number": "22", "pin_name": "PGND", "pin_description": "Ground pin for drivers and LS synchronous FET source terminals. This pin is also the input to zero crossing comparator and overcurrent comparator."}, {"pin_number": "13", "pin_name": "PGOOD1", "pin_description": "Channel1/Channel2 power-good open-drain output. PGOOD1/PGOOD2 is low when the Channel1/Channel2 output voltage is 10% less than the normal regulation point, at onset of OVP events, or during soft start. PGOOD1/PGOOD2 is high impedance when the output is in regulation and the soft-start circuit has terminated. PGOOD1/PGOOD2 is low in shutdown."}, {"pin_number": "28", "pin_name": "PGOOD2", "pin_description": "Channel1/Channel2 power-good open-drain output. PGOOD1/PGOOD2 is low when the Channel1/Channel2 output voltage is 10% less than the normal regulation point, at onset of OVP events, or during soft start. PGOOD1/PGOOD2 is high impedance when the output is in regulation and the soft-start circuit has terminated. PGOOD1/PGOOD2 is low in shutdown."}, {"pin_number": "32", "pin_name": "REFIN2", "pin_description": "Output voltage control for Channel2. Connect REFIN2 to V5FILT for fixed 3.3-V operation. Connect REFIN2 to VREF3 for fixed 1.05-V operation. REFIN2 can be used to program Channel2 output voltage from 0.5 V to 2.5 V."}, {"pin_number": "20", "pin_name": "NC", "pin_description": "No connection."}, {"pin_number": "29", "pin_name": "SKIPSEL", "pin_description": "Low-noise mode control. Connect SKIPSEL to GND for Auto Skip mode operation or to V5FILT for PWM mode (fixed frequency). Connect to VREF2 or leave floating for OOA™ mode operation."}, {"pin_number": "2", "pin_name": "TONSEL", "pin_description": "Frequency select input. Connect to GND for 400-kHz/500-kHz operation. Connect to VREF2 (or leave open) for 400-kHz/300-kHz operation. Connect to V5FILT for 200-kHz/300-kHz operation (5-V/3.3-V SMPS switching frequencies, respectively)."}, {"pin_number": "12", "pin_name": "TRIP1", "pin_description": "Overcurrent trip point set input. Sourcing current is 5 µA at RT with 2900 ppm/°C temperature coefficient."}, {"pin_number": "31", "pin_name": "TRIP2", "pin_description": "Overcurrent trip point set input. Sourcing current is 5 µA at RT with 2900 ppm/°C temperature coefficient."}, {"pin_number": "19", "pin_name": "V5DRV", "pin_description": "Supply voltage for the low-side MOSFET driver DRVL1/DRVL2. Connect a 5-V power source to the V5DRV pin (bypass with 4.7-µF MLCC capacitor to PGND if necessary)."}, {"pin_number": "3", "pin_name": "V5FILT", "pin_description": "5-V analog supply input."}, {"pin_number": "11", "pin_name": "VFB1", "pin_description": "Channel1 feedback input. Connect VFB1 to GND for fixed 5-V operation. Connect VFB1 to V5FILT for fixed 1.5-V operation. Connect VFB1 to a resistive voltage divider from OUT1 to GND to adjust the output from 0.7 V to 5.9 V."}, {"pin_number": "17", "pin_name": "VBST1", "pin_description": "Supply input for high-side MOSFET driver (bootstrap terminal). Connect a capacitor from this pin to the respective LL terminals."}, {"pin_number": "24", "pin_name": "VBST2", "pin_description": "Supply input for high-side MOSFET driver (bootstrap terminal). Connect a capacitor from this pin to the respective LL terminals."}, {"pin_number": "6", "pin_name": "VIN", "pin_description": "Power supply input. VIN supplies power to the linear regulators. The linear regulators are powered by Channel1 if VOUT1 is set greater than 5 V and VSW is tied to VOUT1."}, {"pin_number": "10", "pin_name": "VOUT1", "pin_description": "Output connections to SMPS. These terminals serve two functions: on-time adjustment and output discharge."}, {"pin_number": "30", "pin_name": "VOUT2", "pin_description": "Output connections to SMPS. These terminals serve two functions: on-time adjustment and output discharge."}, {"pin_number": "1", "pin_name": "VREF2", "pin_description": "2-V reference output. Bypass to GND with a 0.1-µF capacitor. VREF2 can source up to 50 µA for external loads."}, {"pin_number": "5", "pin_name": "VREF3", "pin_description": "3.3-V reference output. VREF3 can source up to 10 mA for external loads. Bypass to GND with a 1-µF capacitor."}, {"pin_number": "9", "pin_name": "VSW", "pin_description": "VSW is the switchover source voltage for the LDO when LDOREFIN is connected to GND or V5FILT. Connect VSW to 5 V if LDOREFIN is tied GND. Connect VSW to 3.3 V if LDOREFIN is tied to V5FILT."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "SLUS843B", "path": "self", "date": "2008-09-01", "version": "SLUS843B"}, "family_comparison": "OOB Function Disabled. Refer to TPS51427 for OOB-Enabled Device.", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "28V", "min_input_voltage": "5.5V", "max_output_voltage": "5.9V", "min_output_voltage": "0.5V", "max_output_current": "依赖外部元件", "max_switch_frequency": "0.5MHz", "quiescent_current": "115µA", "high_side_mosfet_resistance": "不适用(外置MOS)", "low_side_mosfet_resistance": "不适用(外置MOS)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "外部反馈电阻可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Auto-Skip/PFM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1%", "output_reference_voltage": "0.7V", "loop_control_mode": "固定导通时间控制"}}