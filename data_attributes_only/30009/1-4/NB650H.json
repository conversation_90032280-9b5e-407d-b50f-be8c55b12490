{"part_number": "NB650", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "NB650/NB650H High-Effeciency, Fast-Transient, 6A, 28V Synchronous Step-Down Converters with 2-Bit VID", "features": ["Wide 4.5V-to-28V Operating Input Range", "6A Output Current", "Internal 50mΩ High-Side, 18mΩ Low-Side Power MOSFETS", "Proprietary Switching Loss Reduction Technique", "1% Reference Voltage", "Programmable Soft-Start Time", "2-bit VID Input", "Soft Shutdown", "Frequency Programmable from 150kHz to 1MHz", "SCP, OCP, OVP, UVP, and Thermal Shutdown", "Optional OCP Protection: Latch-Off Mode (NB650) and Hiccup Mode (NB650H)", "Output Adjustable from 0.6V to 13V", "Available in QFN17 (3mm×4mm) Package"], "description": "The NB650/NB650H is fully-integrated, high-frequency, synchronous, rectified, step-down, switch-mode converters with dynamic-output-voltage control. It offers a very compact solution to achieve 6A of continuous output current over a wide input supply range, and has excellent load and line regulation. The NB650/NB650H operates at high efficiency over a wide output-current-load range. Constant-On-Time control mode provides fast transient response and eases loop stabilization. 2-bit VID inputs support changing the output voltage on-the-fly. Full protection features include short-circuit protection, over-current protection, over-voltage protection, under-voltage protection, and thermal shut down. The NB650/NB650H requires a minimal number of readily-available standard external components, and is available in a space-saving 3mm×4mm QFN17 package.", "applications": ["Notebook Systems and I/O Power", "Networking Systems", "Digital Set Top Boxes", "Flat-Panel Televisions and Monitors", "Distributed Power Systems"], "ordering_information": [{"part_number": "NB650", "order_device": "NB650GL", "status": "NRFND", "package_type": "QFN17", "package_code": "QFN17 (3 x 4mm)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "NB650", "pin_count": 17, "length": 4.0, "width": 3.0, "height": 1.0, "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "0.6-13V", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "NB650", "package_type": "QFN17", "pins": [{"pin_number": "1,2", "pin_name": "SW", "pin_description": "Switch Output. Connect using wide PCB traces."}, {"pin_number": "3", "pin_name": "BST", "pin_description": "Bootstrap. Requires a capacitor between SW and BST to form a floating supply across the high-side switch driver."}, {"pin_number": "4", "pin_name": "PG", "pin_description": "Power Good. Output is an open drain and is high if the output voltage exceeds 90% of the nominal voltage. There is a delay from FB≥90%×Vref to PG goes high."}, {"pin_number": "5", "pin_name": "EN", "pin_description": "EN=1 to enable. For automatic start-up, connect to VIN with a 100kΩ resistor."}, {"pin_number": "6,7", "pin_name": "VID1, VID2", "pin_description": "VID inputs. Control signals for the output-voltage scaling. Acts as the control signals for the internal VID switches. Usually uses an external resistor in parallel with the low-side FB resistor. Changing the VID ON/OFF state changes the FB divider scaling and result in different output voltages."}, {"pin_number": "8", "pin_name": "VCC", "pin_description": "Internal LDO output. The power supply of the internal control circuits. Decouple with 1µF capacitor."}, {"pin_number": "9,10", "pin_name": "GND", "pin_description": "System Ground. The reference ground of the regulated output voltage. Layout requires extra care."}, {"pin_number": "11", "pin_name": "IN", "pin_description": "Supply Voltage. Operates from a 4.5V-to-28V input rail. Requires C1 to decouple the input rail. Connect using wide PCB traces."}, {"pin_number": "12", "pin_name": "AGND", "pin_description": "Analog Ground."}, {"pin_number": "13", "pin_name": "FB", "pin_description": "Feedback. Connect to the tap of an external resistor divider from the output to GND to set the output voltage."}, {"pin_number": "14,15", "pin_name": "RFB2, RFB1", "pin_description": "Drain of the internal VID switches. Typically uses an external resistor in parallel with the low-side FB resistor along with the internal VID switch to change the ON/OFF state of the VID switching to change the FB divider scaling and result in different output voltages."}, {"pin_number": "16", "pin_name": "SS", "pin_description": "Soft-Start. Connect an external capacitor to program the soft-start time for the switch-mode regulator."}, {"pin_number": "17", "pin_name": "FREQ", "pin_description": "Frequency Set during CCM. The input voltage and the frequency-set resistor between the IN and FREQ pin determines the ON period. For best results, use an ON period longer than 200ns."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "NB650/NB650H Rev. 1.13", "path": "NB650_NB650H.pdf", "release_date": "2019-10-01", "version": "1.13"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 22.5, "min_input_voltage": 4.5, "max_output_voltage": 13, "min_output_voltage": 0.6, "max_output_current": 6, "max_switch_frequency": 1, "quiescent_current": 400, "high_side_mosfet_resistance": 50, "low_side_mosfet_resistance": 18, "over_current_protection_threshold": 10, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "未找到", "output_discharge": "未找到", "integrated_ldo": "Yes", "frequency_synchronization": "未找到", "output_voltage_tracking": "未找到", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": 1, "output_reference_voltage": 0.6, "loop_control_mode": "固定导通时间控制", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "22.5V", "min_input_voltage": "4.5V", "max_output_voltage": "13V", "min_output_voltage": "0.6V", "max_output_current": "6A", "max_switch_frequency": "1MHz", "quiescent_current": "400µA", "high_side_mosfet_resistance": "50mΩ", "low_side_mosfet_resistance": "18mΩ", "over_current_protection_threshold": "10A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "VID", "enable_function": "Yes", "light_load_mode": "PSM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "未找到", "output_discharge": "未找到", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.6V", "loop_control_mode": "固定导通时间控制"}}