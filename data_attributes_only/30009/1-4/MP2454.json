{"part_number": "MP2454", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC变换器", "category_lv3": "降压", "part_number_title": "36V, 0.6A Step-Down Converter", "features": ["60μA Operating Quiescent Current", "Wide 3.3V to 36V Operating Input Range", "200mΩ Internal Power MOSFET", "Up to 2.3MHz Programmable Switching Frequency", "Stable with Ceramic Output Capacitors", "Internal Compensation", "External Soft Start", "> 90% Efficiency", "Low Dropout Operation for Cold Crank", "3.5μA Low Shutdown Supply Current", "Synchronization to External Clock", "Power Good Output", "Programmable Power Good Delay Time", "MSOP-10 EP and QFN-10 (3mm x 3mm) Packages"], "description": "The MP2454 is a frequency-programmable (350kHz to 2.3MHz), step-down switching regulator with an integrated internal high-side, high-voltage power MOSFET. It outputs efficiently up to 0.6A and has current-mode control for fast loop response. The wide 3.3V to 36V input range accommodates a variety of step-down applications in automotive-input environments. A 3.5μA shutdown mode quiescent current allows for use in battery-powered applications. Also, the device has a high duty cycle and low drop-out mode for automotive cold-crank conditions. The MP2454 achieves high-power conversion efficiency over a wide load range by scaling down the switching frequency at light-load conditions to reduce both switching and gate driving losses. Frequency foldback prevents inductor current runaway during start-up and short circuit. Thermal shutdown provides reliable, fault-tolerant operation. An open-drain power good (PG) signal indicates when the output is within its nominal voltage. The MP2454 is available in MSOP-10 EP and QFN-10 (3mm x 3mm) packages.", "applications": ["High-Voltage Power Conversion", "Automotive Systems", "Industrial Power Systems", "Distributed Power Systems", "Battery Powered Systems"], "ordering_information": [{"part_number": "MP2454", "order_device": "MP2454GH", "status": "Active", "package_type": "MSOP-10 EP", "package_code": "MSOP-10 EP", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MO-187, VARIATION BA-T", "marking": "YWLLL M2454", "pin_count": 10, "length": 3.1, "width": 3.1, "height": 1.1, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "Automotive"}, {"part_number": "MP2454", "order_device": "MP2454GQ", "status": "Active", "package_type": "QFN-10", "package_code": "QFN-10 (3mm x 3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": null, "package_drawing_code": "MO-229, VARIATION VEED-5", "marking": "AEFY LLL", "pin_count": 10, "length": 3.1, "width": 3.1, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "Automotive"}], "typical_application_circuit": "是", "pin_config": "是", "function_block_diagram": "是", "pin_function": [{"product_part_number": "MP2454", "package_type": "MSOP-10 EP / QFN-10", "pins": [{"pin_number": "1", "pin_name": "SW", "pin_description": "Switch node. The output from the high-side switch. SW requires a low VF Schottky diode to ground (close to SW) to reduce switching spikes."}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Input supply. VIN provides power to all the internal control circuitry (both the BST regulators and the high-side switch). VIN requires a decoupling capacitor to ground (close to VIN) to minimize the switching spikes."}, {"pin_number": "3", "pin_name": "GND", "pin_description": "Ground. Place the output capacitor as close to GND as possible to shorten the high-current switching paths."}, {"pin_number": "4", "pin_name": "SS", "pin_description": "Soft start. Place a capacitor from SS to SGND to set the soft-start period. The MP2454 sources 1.8µA from SS to the soft-start capacitor at start-up. As the SS voltage rises, the feedback threshold voltage increases to limit the inrush current during start-up."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Feedback. Connect FB to the tap of the external resistor divider. The feedback threshold voltage is 0.8V."}, {"pin_number": "6", "pin_name": "POKDL", "pin_description": "POK signal delay. Connect a capacitor from POKDL to GND to program the POK signal delay time."}, {"pin_number": "7", "pin_name": "POK", "pin_description": "Open-drain power good output. POK goes high when Vo is within the ±10% window of the nominal voltage. POK is pulled down during shutdown."}, {"pin_number": "8", "pin_name": "FREQ", "pin_description": "Switching frequency program. Connect a resistor from FREQ to ground to set the switching frequency."}, {"pin_number": "9", "pin_name": "EN/SYNC", "pin_description": "Enable and SYNC input. Pull EN/SYNC below the specified threshold to shut the chip down. Pull EN/SYNC above the specified threshold to enable the chip. Floating EN/SYNC shuts the chip down. Apply a clock signal (350kHz to 2.3MHz) to synchronize the internal oscillator frequency to the external clock."}, {"pin_number": "10", "pin_name": "BST", "pin_description": "Bootstrap. The positive power supply for the internal floating high-side MOSFET driver. Connect a bypass capacitor between BST and SW."}, {"pin_number": "Exposed Pad", "pin_name": "Exposed Pad", "pin_description": "Connect the exposed pad to the GND plane to optimize thermal performance."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MP2454-36V, 0.6A, STEP-DOWN CONVERTER", "datasheet_path": "用户上传文件", "release_date": "2015-09-18", "version": "1.01"}, "family_comparison": "未找到", "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 36, "min_input_voltage": 3.3, "max_output_voltage": "可调", "min_output_voltage": "可调", "max_output_current": 0.6, "max_switch_frequency": 2.3, "quiescent_current": 60, "high_side_mosfet_resistance": 200, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 1.8, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "脉冲跳跃模式(PSM)", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 2, "output_reference_voltage": 0.8, "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "3.3V", "max_output_voltage": "Adjustable", "min_output_voltage": "0.8V", "max_output_current": "0.6A", "max_switch_frequency": "2.3MHz", "quiescent_current": "60µA", "high_side_mosfet_resistance": "200mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "1.8A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "脉冲跳跃模式(PSM)", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}