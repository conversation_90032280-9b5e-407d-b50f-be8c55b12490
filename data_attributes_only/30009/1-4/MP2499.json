{"part_number": "MP2499", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "EOL", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "Integrated 100V Load Dump Protection 2A, 100kHz Step Down Regulator with Programmable Output Current", "features": ["Replaces External Transorb", "Supports 12V/24V Systems", "Input Surge Protection Up to 100V", "Programmable Output Current up to 2.0A", "Output Adjustable", "Fixed 100kHz Frequency", "0.25Ω Internal Power MOSFET Switch", "Stable with Low ESR Output Ceramic Capacitors", "92% Efficiency @ 500mA (Vo=5V)", "Thermal Shutdown", "Cycle-by-Cycle Over Current Protection", "Available in a 16-Pin SOIC Package"], "description": "The MP2499 is a monolithic step-down switch mode converter with a programmable output current limit and an integrated input over-voltage protection switch. It achieves 2.0A continuous output current over a wide input supply range with excellent load and line regulation. The maximum output current can be programmed by sensing current through the inductor DC resistance (DCR) or an accurate sense resistor. Fault condition protection includes cycle-by-cycle current limiting and thermal shutdown. The MP2499 can survive high-voltage transients such as those found in automotive and industrial applications. The MP2499 requires a minimum number of readily available standard external components. The MP2499 is available in a 16-pin SOIC package.", "applications": ["12V/24V Systems with High Input Surge", "Automotive Cigarette Lighter Adapters", "Power Supply for Linear Chargers", "Industrial Power Supplies", "Avionics"], "ordering_information": [{"part_number": "MP2499", "order_device": "MP2499DS", "status": "EOL", "package_type": "SOIC16", "package_code": "SOIC16", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "MS-012, VARIATION AC", "marking": "MP2499DS", "pin_count": "16", "length": "10.00", "width": "4.00", "height": "1.75", "pitch": "1.27", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MP2499", "package_type": "SOIC16", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "Feedback. An external resistor divider from the output to GND, tapped to the FB pin sets the output voltage. To prevent current limit run away during a short circuit fault condition the frequency-fold-back comparator lowers the oscillator frequency when the FB voltage is below 250mV."}, {"pin_number": "2", "pin_name": "SS", "pin_description": "Connect to an external capacitor used for Soft-Start and compensation for current limiting loop."}, {"pin_number": "3", "pin_name": "ISP", "pin_description": "Positive Current Sense"}, {"pin_number": "4", "pin_name": "ISN", "pin_description": "Negative Current Sense Input for load current limiting."}, {"pin_number": "5", "pin_name": "CTR", "pin_description": "Control pin. Tie a zener diode from CTR to ground. The zener voltage should equal to normal output voltage."}, {"pin_number": "6", "pin_name": "N/C", "pin_description": "No connection"}, {"pin_number": "7", "pin_name": "IN", "pin_description": "Input. Connect input power supply, which may have surge voltage to IN pin."}, {"pin_number": "8", "pin_name": "IN", "pin_description": "Input. Connect input power supply, which may have surge voltage to IN pin."}, {"pin_number": "9", "pin_name": "OUT", "pin_description": "Output Pin. Connect to VDD pin."}, {"pin_number": "10", "pin_name": "OUT", "pin_description": "Output Pin. Connect to VDD pin."}, {"pin_number": "11", "pin_name": "N/C", "pin_description": "No connection"}, {"pin_number": "12", "pin_name": "BST", "pin_description": "Bootstrap. This capacitor is needed to drive the power switch's gate above the supply voltage. It is connected between SW and BST pins to form a floating supply across the power switch driver. An on-chip regulator is used to charge up the external bootstrap capacitor. If the on-chip regulator is not strong enough, an optional diode can be connected from IN or OUT to charge the external bootstrap capacitor."}, {"pin_number": "13", "pin_name": "SW", "pin_description": "Switch Output. It is the source of power device."}, {"pin_number": "14", "pin_name": "VDD", "pin_description": "Supply Voltage Bypass pin. This pin is also the output of the OV protection switch. The MP2499 operates from a +5V to +36V unregulated input. CIN is needed to prevent large voltage spikes from appearing at the input. Put CIN as close to the IC as possible. It is the drain of the internal power device and power supply for the whole chip."}, {"pin_number": "15", "pin_name": "GND", "pin_description": "Ground. This pin is the voltage reference for the regulated output voltage. For this reason care must be taken in its layout. This node should be placed outside of the D1 to CIN ground path to prevent switching current spikes from inducing voltage noise into the part."}, {"pin_number": "16", "pin_name": "EN", "pin_description": "On/Off Control Input."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MP2499.pdf", "datasheet_path": "local", "release_date": "2010-11-24", "version": "0.91"}, "family_comparison": "未找到", "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "100", "min_input_voltage": "3.3", "max_output_voltage": "5", "min_output_voltage": "3.3", "max_output_current": "2", "max_switch_frequency": "0.1", "quiescent_current": "500", "high_side_mosfet_resistance": "250", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "3", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "未找到", "output_under_voltage_protection": "未找到", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "2.5", "output_reference_voltage": "0.8", "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "100V", "min_input_voltage": "3.3V", "max_output_voltage": "5V", "min_output_voltage": "0.8V", "max_output_current": "2A", "max_switch_frequency": "100kHz", "quiescent_current": "500µA", "high_side_mosfet_resistance": "250mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "3A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "Auto Recovery", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}