{"part_number": "TPS51397A", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "ULQ™ 运行的 TPS51397A 4.5V 至 24V、10A 同步降压转换器", "features": ["输入电压范围: 4.5V 至 24V", "输出电压范围: 0.6V 至 5.5V", "支持 10A 的连续输出电流", "D-CAP3™ 架构控制,可实现快速瞬态响应", "0.6V ± 1% 反馈电压精度 (25°C)", "集成 17mΩ 和 5.9mΩ FET", "ULQ™ 运行 (110µA),能够在系统待机期间延长电池寿命", "可通过 MODE 引脚选择 Eco-mode™ 和无声™", "500kHz 和 800kHz 可选开关频率", "可调内部软启动时间,默认为 1.2ms", "大占空比运行", "集成式电源正常状态指示器", "内置输出放电功能", "逐周期过流保护", "锁存输出 OV 和 UV 保护", "非锁存 UVLO 和 OT 保护", "-40℃ 至 125℃ 的工作结温范围", "20 引脚 3.0mm × 3.0mm HotRod™ VQFN 封装", "与 12A TPS56C230 引脚对引脚兼容", "利用 TPS51397A 并借助 WEBENCH® Power Designer 创建定制设计方案"], "description": "该器件是单片 10A 同步降压转换器,集成了 MOSFET,简单易用且高效,只需极少的外部组件,适合空间受限的电源系统。TPS51397A 采用了 D-CAP3™ 控制,此控制方式只需内部补偿即可实现快速瞬态响应以及出色的线路和负载调整。ULQ™ (超低静态电流) 特性则非常有益于在低功耗运行时延长电池寿命。输入电压较低时,大负荷运行可显著改善负载瞬态性能。可使用 MODE 引脚来设置 Eco-mode™ 或无声™ (OOA) 模式,以实现轻负载运行以及 500kHz 或 800kHz 的开关频率。Eco-mode™ 可在轻负载运行期间维持高效率。OOA 模式可将开关频率保持在可闻频率以上,同时将对效率的影响降至最低。此器件同时支持内部和外部软启动选项。它具有 1.2ms 的内部固定软启动时间。如果应用需要更长的软启动时间,可将外部 SS 引脚连接至外部电容器。TPS51397A 集成了电源正常状态指示器并具备输出放电功能。它提供包括 OVP、UVP、OCP、OTP 和 UVLO 在内的全面保护。该器件可采用 20 引脚 3.0mm × 3.0mm HotRod™ 封装,额定结温范围为 -40℃ 至 125°C。", "applications": ["笔记本电脑和台式机", "超极本、手持平板电脑", "工业 PC、单板计算机", "非军用无人机", "分布式电源系统"], "ordering_information": [{"part_number": "TPS51397A", "order_device": "TPS51397ARJER", "status": "Active", "package_type": "VQFN-HR", "package_code": "RJE", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RJE0020B", "marking": "51397A", "pin_count": "20", "length": "3.0", "width": "3.0", "height": "1.0", "pitch": "0.45", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS51397A", "order_device": "TPS51397ARJER.A", "status": "Active", "package_type": "VQFN-HR", "package_code": "RJE", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RJE0020B", "marking": "51397A", "pin_count": "20", "length": "3.0", "width": "3.0", "height": "1.0", "pitch": "0.45", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS51397A", "order_device": "TPS51397ARJER.B", "status": "Active", "package_type": "VQFN-HR", "package_code": "RJE", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RJE0020B", "marking": "51397A", "pin_count": "20", "length": "3.0", "width": "3.0", "height": "1.0", "pitch": "0.45", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS51397A", "package_type": "VQFN (20)", "pins": [{"pin_number": "1", "pin_name": "BST", "pin_description": "Supply input for the gate drive voltage of the high-side MOSFET. Connect the bootstrap capacitor between BST and SW. 0.1 µF is recommended."}, {"pin_number": "2,3,4,5", "pin_name": "VIN", "pin_description": "Input voltage supply pin for the control circuitry. Connect the input decoupling capacitors between VIN and PGND."}, {"pin_number": "6,19,20", "pin_name": "SW", "pin_description": "Switching node connection to the inductor and bootstrap capacitor for buck. This pin voltage swings from a diode voltage below the ground up to input voltage of buck."}, {"pin_number": "7,8,18, Thermal Pad", "pin_name": "PGND", "pin_description": "Power GND terminal for the controller circuit and the internal circuitry"}, {"pin_number": "9", "pin_name": "PGOOD", "pin_description": "Open-drain power-good indicator. It is asserted low if output voltage is out of PG threshold, over voltage, or if the device is under thermal shutdown, EN shutdown, or during soft start."}, {"pin_number": "11", "pin_name": "SS", "pin_description": "Soft-Start time selection pin. Connecting an external capacitor sets the soft-start time and if no external capacitor is connected, the soft-start time is about 1.2 ms."}, {"pin_number": "10,16", "pin_name": "NC", "pin_description": "Not connect. Can be connected to GND plane for better thermal achieved."}, {"pin_number": "12", "pin_name": "EN", "pin_description": "Enable input of buck converter"}, {"pin_number": "13", "pin_name": "AGND", "pin_description": "Ground of internal analog circuitry. Connect AGND to GND plane with a short trace."}, {"pin_number": "14", "pin_name": "FB", "pin_description": "Feedback sensing pin for Buck output voltage. Connect this pin to the resistor divider between output voltage and AGND."}, {"pin_number": "15", "pin_name": "MODE", "pin_description": "Switching frequency and light load operation mode selection pin. Connect this pin to a resistor divider from VCC and AGND for different MODE options shown in 表 7-1."}, {"pin_number": "17", "pin_name": "VCC", "pin_description": "The driver and control circuits are powered from this voltage. Decouple with a minimum 1-μF ceramic capacitor as close to VCC as possible."}]}], "datasheet_cn": {"datasheet_name": "TPS51397A_ZHCSLU8A.pdf", "datasheet_path": "未找到", "release_date": "2020-10", "version": "ZHCSLU8A"}, "datasheet_en": {"datasheet_name": "SLUSDX7", "datasheet_path": "未找到", "release_date": "未找到", "version": "SLUSDX7"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 24, "min_input_voltage": 4.5, "max_output_voltage": 5.5, "min_output_voltage": 0.6, "max_output_current": 10, "max_switch_frequency": 0.8, "quiescent_current": 110, "high_side_mosfet_resistance": 17, "low_side_mosfet_resistance": 5.9, "over_current_protection_threshold": 12, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "Eco-mode", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1, "output_reference_voltage": 0.6, "loop_control_mode": "固定导通时间控制", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "24V", "min_input_voltage": "4.5V", "max_output_voltage": "5.5V", "min_output_voltage": "0.6V", "max_output_current": "10A", "max_switch_frequency": "0.8MHz", "quiescent_current": "110µA", "high_side_mosfet_resistance": "17mΩ", "low_side_mosfet_resistance": "5.9mΩ", "over_current_protection_threshold": "12A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Eco-mode, OOA", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.6V", "loop_control_mode": "固定导通时间控制"}}