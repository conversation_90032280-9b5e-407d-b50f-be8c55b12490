{"part_number": "MP2371", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND (Not Recommended for New Designs)", "application_grade": "工业级", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片", "part_number_title": "MP2371 1.8A, 24V, 700KHz Step-Down Converter", "features": ["2.5A Peak Output Current", "1.8A Continuous Output Current", "0.3Ω Internal Power MOSFET Switch", "Stable with Low ESR Output Ceramic Capacitors", "0.1μA Shutdown Mode", "Fixed 700KHz Frequency", "Thermal Shutdown", "Cycle-by-Cycle Over Current Protection", "Wide 4.5V to 24V Operating Input Range", "Output Adjustable from 0.81V to 15V", "Available in 2x2 QFN8 Package"], "description": "The MP2371 is a monolithic step-down switch mode converter with a built-in internal power MOSFET. It achieves 1.8A continuous output current over a wide input supply range with excellent load and line regulation. Current mode operation provides fast transient response and eases loop stabilization. Fault condition protection includes cycle-by-cycle current limiting and thermal shutdown. The MP2371 requires a minimum number of readily available standard external components. The MP2371 is available in a 2mm x 2mm QFN8 package.", "applications": ["Broadband Communications Equipment", "Digital Entertainment Systems", "Distributed Power Systems", "Battery Charger", "Pre-Regulator for Linear Regulators"], "ordering_information": [{"part_number": "MP2371", "order_device": "MP2371DG", "status": "NRFND (Not Recommended for New Designs)", "package_type": "QFN8", "package_code": "QFN8 (2mm x 2mm)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "未找到", "marking": "未找到", "pin_count": 8, "length": 2.0, "width": 2.0, "height": "未找到", "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "可调", "application_grade": "工业级"}], "typical_application_circuit": "是", "pin_config": "是", "function_block_diagram": "是", "pin_function": [{"product_part_number": "MP2371", "package_type": "QFN8", "pins": [{"pin_number": "1, 8", "pin_name": "NC", "pin_description": "No Connect."}, {"pin_number": "2", "pin_name": "SW", "pin_description": "Switch Output."}, {"pin_number": "3", "pin_name": "IN", "pin_description": "Supply Voltage. The MP2371 operates from a +4.5V to +24V unregulated input. C1 is needed to prevent large voltage spikes from appearing at the input."}, {"pin_number": "4", "pin_name": "EN", "pin_description": "On/Off Control Input. Pull above 1.5V to turn the device on."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Feedback. An external resistor divider from the output to GND, tapped to the FB pin sets the output voltage. To prevent current limit run away during a short circuit fault condition the frequency foldback comparator lowers the oscillator frequency when the FB voltage is below 250mV."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Ground. This pin is the voltage reference for the regulated output voltage. For this reason care must be taken in its layout. This node should be placed outside of the D1 to C1 ground path to prevent switching current spikes from inducing voltage noise into the part."}, {"pin_number": "7", "pin_name": "BST", "pin_description": "Bootstrap. This capacitor is needed to drive the power switch's gate above the supply voltage. It is connected between SW and BST pins to form a floating supply across the power switch driver."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MP2371-1.8A, 24V, 700KHz Step-Down Converter", "datasheet_path": "用户上传文件", "release_date": "2/26/2020", "version": "1.0"}, "family_comparison": "推荐替代型号为 MP2392", "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 24, "min_input_voltage": 4.5, "max_output_voltage": 15, "min_output_voltage": 0.81, "max_output_current": 1.8, "max_switch_frequency": 0.7, "quiescent_current": 800, "high_side_mosfet_resistance": 300, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 2.5, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 2.47, "output_reference_voltage": 0.81, "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "24V", "min_input_voltage": "4.5V", "max_output_voltage": "15V", "min_output_voltage": "0.81V", "max_output_current": "1.8A", "max_switch_frequency": "700kHz", "quiescent_current": "800µA", "high_side_mosfet_resistance": "300mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "2.5A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.47%", "output_reference_voltage": "0.81V", "loop_control_mode": "峰值电流模式"}}