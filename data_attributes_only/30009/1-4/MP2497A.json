{"part_number": "MP2497-A", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC变换器", "category_lv3": "降压芯片(<PERSON>)", "part_number_title": "3A, 50V, 100kHz Step-Down Converter with Programmable Output OVP Threshold", "features": ["Wide 4.5V to 50V Operating Input Range", "Programmable Output Over Voltage Protection", "Output Adjustable from 0.8V to 25V", "0.15Ω Internal Power MOSFET Switch", "Internal 4ms Soft Start", "Stable with Low ESR Output Ceramic Capacitors", "Fixed 100kHz Frequency", "Fast Switching Speed", "Thermal Shutdown", "Output Line Drop Compensation", "Hiccup Circuit Limit and Short Circuit Protection", "Available in SOIC8E Package"], "description": "The MP2497-A is a monolithic step-down switch mode converter with a programmable output current limit. The MP2497-A is the high efficiency version of MP2497. It has fast switching speed thus the switching loss is greatly reduced. It achieves 3A continuous output current over a wide input supply range with excellent load and line regulation. An internal 2-4ms soft start prevents inrush current at turning on. And it is capable of providing output line drop compensation. Fault condition protection includes hiccup current limit and short circuit protection, programmable output over voltage protection and thermal shutdown. The MP2497-A requires a minimum number of readily available standard external components. The MP2497-A is available in SOIC8E package.", "applications": ["USB Power Supplies", "Automotive Cigarette Lighter Adapters", "Power Supply for Linear Chargers"], "ordering_information": [{"part_number": "MP2497-A", "order_device": "MP2497GN-A", "status": "Active", "package_type": "SOIC8E", "package_code": "SOIC8E", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "SOIC8E (EXPOSED PAD)", "marking": "MP2497-A", "pin_count": 8, "length": 5.0, "width": 4.0, "height": 1.7, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}, {"part_number": "MP2497-A", "order_device": "MP2497GN-A-Z", "status": "Active", "package_type": "SOIC8E", "package_code": "SOIC8E", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "SOIC8E (EXPOSED PAD)", "marking": "MP2497-A", "pin_count": 8, "length": 5.0, "width": 4.0, "height": 1.7, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": "有", "pin_config": "有", "function_block_diagram": "有", "pin_function": [{"product_part_number": "MP2497-A", "package_type": "SOIC8E", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "Supply Voltage. The MP2497-A operates from a +4.5V to +50V unregulated input. CIN is needed to prevent large voltage spikes from appearing at the input. Put Cin as close to the IC as possible. It is the drain of the internal power device and power supply for the whole chip."}, {"pin_number": "2", "pin_name": "GND, Exposed Pad", "pin_description": "Ground. This pin is the voltage reference for the regulated output voltage. For this reason care must be taken in its layout. This node should be placed outside of the D1 to CIN ground path to prevent switching current spikes from inducing voltage noise into the part. Connect exposed pad to GND plane for optimal thermal performance."}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback. An external resistor divider from the output to GND tapped to the FB pin sets the output voltage. To prevent current limit run away during a short circuit fault condition the frequency-fold-back comparator lowers the oscillator frequency when the FB voltage is below 250mV."}, {"pin_number": "4", "pin_name": "OVP", "pin_description": "Output Over Voltage Protection. Connect OVP to the center point of an external resistor divider from output to GND. The OVP reference is 1.23V."}, {"pin_number": "5", "pin_name": "ISN", "pin_description": "Negative Current Sense Input. It is used for load current limiting and output line drop compensation."}, {"pin_number": "6", "pin_name": "ISP", "pin_description": "Positive Current Sense Input. It is used for load current limiting and output line drop compensation."}, {"pin_number": "7", "pin_name": "BST", "pin_description": "Bootstrap. This capacitor is needed to drive the power switch's gate above the supply voltage. It is connected between SW and BST pins to form a floating supply across the power switch driver. An on-chip regulator is used to charge up the external boot-strap capacitor. If the on-chip regulator is not strong enough, one optional diode can be connected from IN or OUT to charge the external boot-strap capacitor."}, {"pin_number": "8", "pin_name": "SW", "pin_description": "Switch Output. It is the source of power device."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "MP2497-A Rev. 1.0", "path": "用户上传文件", "release_date": "2012-05-30", "version": "1.0"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "50V", "min_input_voltage": "4.5V", "max_output_voltage": "25V", "min_output_voltage": "0.8V", "max_output_current": "3A", "max_switch_frequency": "120kHz", "quiescent_current": "1200µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "5A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "无", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "2.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}