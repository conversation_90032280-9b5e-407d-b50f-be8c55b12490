{"part_number": "MP9485", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC变换器", "category_lv3": "降压型", "part_number_title": "100V Input, 500mA, Step-Down Converter", "features": ["Wide 4.5V to 100V Input Range", "Hysteretic Control: No Compensation", "Up to 2MHz Switching Frequency", "PWM Dimming Control Input for LED Application", "Short-Circuit Protection (SCP) with Integrated High-Side MOSFET", "170μA Quiescent Current", "Thermal Shutdown", "Available in a SOIC-8 Package with an Exposed Pad"], "description": "The MP9485 is a high-voltage, step-down, switching regulator that delivers up to 0.5A of current to the load. It integrates a high-side, high-voltage power MOSFET with a current limit of 1.0A, typically. The wide 4.5V to 100V input range accommodates a variety of step-down applications, making it ideal for automotive, industry, and lighting applications. Hysteretic voltage-mode control is employed for very fast response. MPS's proprietary feedback control scheme minimizes the number of required external components.\nThe switching frequency can be up to 2MHz, allowing for small component size. Thermal shutdown and short-circuit protection (SCP) provide reliable and fault-tolerant operations. A 170µA quiescent current allows the MP9485 to be used in battery-powered applications.\nThe MP9485 is available in a SOIC-8 package with an exposed pad.", "applications": ["E-Bike Control Power Supplies", "Solar Energy Systems", "Automotive System Power", "Industrial Power Supplies", "High-Power LED Drivers"], "ordering_information": [{"part_number": "MP9485", "order_device": "MP9485GN", "status": "Active", "package_type": "SOIC-8 EP", "package_code": "SOIC-8 EP", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "JEDEC MS-012, VARIAT<PERSON> BA", "marking": "MP9485 LLLLLLLL MPSYWW", "pin_count": 8, "length": "4.9", "width": "3.9", "height": "1.5", "pitch": "1.27", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}, {"part_number": "MP9485", "order_device": "MP9485GN-Z", "status": "Active", "package_type": "SOIC-8 EP", "package_code": "SOIC-8 EP", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "JEDEC MS-012, VARIAT<PERSON> BA", "marking": "MP9485 LLLLLLLL MPSYWW", "pin_count": 8, "length": "4.9", "width": "3.9", "height": "1.5", "pitch": "1.27", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "MP9485", "package_type": "SOIC-8 EP", "pins": [{"pin_number": "1", "pin_name": "FB", "pin_description": "Feedback. FB is the input to the voltage feedback hysteretic comparator."}, {"pin_number": "2", "pin_name": "NC", "pin_description": "No connection. NC is not connected."}, {"pin_number": "3", "pin_name": "VIN", "pin_description": "Input supply. VIN supplies power to all of the internal control circuitries, both BST regulators, and the high-side switch. A decoupling capacitor to ground must be placed close to VIN to minimize switching spikes."}, {"pin_number": "4", "pin_name": "BST", "pin_description": "Bootstrap. BST is the positive power supply for the internal, floating, high-side MOSFET driver. Connect a bypass capacitor between BST and SW."}, {"pin_number": "5", "pin_name": "SW", "pin_description": "Switch node. SW is the output from the high-side switch. A low forward voltage (VF) <PERSON><PERSON>tky rectifier to ground is required. The rectifier must be placed close to SW to reduce switching spikes."}, {"pin_number": "6", "pin_name": "DIM", "pin_description": "PWM dimming input. DIM is useful in LED driver applications. Pull DIM below the specified threshold for dimming off; pull DIM above the specified threshold for dimming on. If there is no need for a dimming function, such as in common buck applications, then connect DIM and EN together."}, {"pin_number": "7", "pin_name": "EN", "pin_description": "Enable input. Pull EN below the specified threshold to shut down the MP9485; pull EN above the specified threshold or leave EN floating to enable the MP9485."}, {"pin_number": "8", "pin_name": "GND", "pin_description": "Ground. GND should be placed as close to the output capacitor as possible to avoid the high-current switch paths. Connect the exposed pad to the GND plane for optimal thermal performance."}]}], "datasheet_en": {"datasheet_name": "MP9485 Datasheet", "datasheet_path": "用户上传", "release_date": "2019-10-10", "version": "1.11"}, "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 95, "min_input_voltage": 4.5, "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": 0.5, "max_switch_frequency": 2, "quiescent_current": 170, "high_side_mosfet_resistance": 500, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 0.75, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "未找到", "output_reference_voltage": 0.2, "loop_control_mode": "迟滞模式控制", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "95V", "min_input_voltage": "4.5V", "max_output_voltage": "未找到", "min_output_voltage": "未找到", "max_output_current": "0.5A", "max_switch_frequency": "2MHz", "quiescent_current": "170µA", "high_side_mosfet_resistance": "500mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "0.75A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "无", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±3%", "output_reference_voltage": "0.2V", "loop_control_mode": "迟滞模式控制"}}