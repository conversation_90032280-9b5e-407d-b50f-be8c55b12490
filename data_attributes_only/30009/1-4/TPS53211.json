{"part_number": "TPS53211", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "DC-DC控制器", "part_number_title": "具有轻载效率优化的单相脉宽调制(PWM) 控制器", "features": ["1.5V至19V 转换电压范围", "4.5V 至 14V 电源电压范围", "电压模式控制", "针对效率优化的轻负载跳跃模式", "高精度0.5% 内部 0.8V 基准", "0.8V 至0.7 × VIN的可调输出电压", "内部软启动", "支持预偏置启动", "支持软启动", "从250kHz至1MHz 的可编程开关频率", "过流保护", "针对过流的电感器直流电阻(DCR) 感测", "针对零电流检测的 RDS(on) 感测", "过压和欠压保护", "开漏电源正常输出", "内部自举开关", "由VCCDR 供电的集成高电流驱动器", "小型3mm×3mm的16引脚四方扁平无引线(QFN) 封装"], "description": "TPS53211 是一款具有集成高电流驱动器的单相位 PWM 控制器。它用于1.5V 到最高 19V 的转换电压。TPS53211 特有一个跳跃模式解决方案,此解决方案在无需增加输出电压纹波的情况下可优化轻负载条件下的效率。此器件提供预偏置启动,软停止,集成自举开关,电源正常功能,EN / 输入欠压闭锁 (UVLO) 保护。它支持高达19V的转换电压,以及0.8V 至0.7V × VIN 的输出电压可调范围。TPS53211 采用3mm×3mm,16引脚,QFN封装(符合绿色环保 RoHs 标准且无铅),并且可在 -40°C 到85℃的温度范围内额定运行。", "applications": ["服务器和台式机子系统电源", "DDR 内存和端接电源", "分布式电源", "通用直流/直流转换器"], "ordering_information": [{"part_number": "TPS53211", "order_device": "TPS53211RGTR", "status": "Active", "package_type": "VQFN", "package_code": "RGT", "carrier_description": "LARGE T&R", "carrier_quantity": 3000, "package_drawing_code": "RGT0016C", "marking": "53211", "pin_count": 16, "length": 3.0, "width": 3.0, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "不适用", "application_grade": "Industrial"}, {"part_number": "TPS53211", "order_device": "TPS53211RGTR.B", "status": "Active", "package_type": "VQFN", "package_code": "RGT", "carrier_description": "LARGE T&R", "carrier_quantity": 3000, "package_drawing_code": "RGT0016C", "marking": "53211", "pin_count": 16, "length": 3.0, "width": 3.0, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "不适用", "application_grade": "Industrial"}, {"part_number": "TPS53211", "order_device": "TPS53211RGTT", "status": "Active", "package_type": "VQFN", "package_code": "RGT", "carrier_description": "SMALL T&R", "carrier_quantity": 250, "package_drawing_code": "RGT0016C", "marking": "53211", "pin_count": 16, "length": 3.0, "width": 3.0, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "不适用", "application_grade": "Industrial"}, {"part_number": "TPS53211", "order_device": "TPS53211RGTT.B", "status": "Active", "package_type": "VQFN", "package_code": "RGT", "carrier_description": "SMALL T&R", "carrier_quantity": 250, "package_drawing_code": "RGT0016C", "marking": "53211", "pin_count": 16, "length": 3.0, "width": 3.0, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "不适用", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS53211", "package_type": "QFN", "pins": [{"pin_number": "1", "pin_name": "VCCDR", "pin_description": "集成驱动器的偏置电压 (Bias voltage for integrated drivers.)"}, {"pin_number": "2", "pin_name": "LGATE", "pin_description": "低侧栅极驱动输出 (Low-side gate drive output.)"}, {"pin_number": "3", "pin_name": "PHASE", "pin_description": "输出电感连接到集成电源设备 (Output inductor connection to integrated power devices.)"}, {"pin_number": "4", "pin_name": "BOOT", "pin_description": "高侧驱动的电源输入（自举引脚）。将电容器从此引脚连接到SW引脚 (Supply input for high-side drive (boot strap pin). Connect capacitor from this pin to SW pin)"}, {"pin_number": "5", "pin_name": "UGATE", "pin_description": "高侧栅极驱动输出 (High-side gate drive output.)"}, {"pin_number": "6", "pin_name": "PGOOD", "pin_description": "电源正常输出标志。开漏输出。通过电阻上拉到外部电源轨 (Power good output flag. Open drain output. Pull up to an external rail via a resistor.)"}, {"pin_number": "7", "pin_name": "EN", "pin_description": "使能 (Enable.)"}, {"pin_number": "8", "pin_name": "COMP", "pin_description": "误差放大器补偿端。通常建议使用III型补偿方法以保证稳定性 (Error amplifier compensation terminal. Type III compensation method is generally recommended for stability.)"}, {"pin_number": "9", "pin_name": "FB", "pin_description": "电压反馈。用于OVP、UVP和PGD确定 (Voltage feedback. Use for OVP, UVP and PGD determination.)"}, {"pin_number": "10", "pin_name": "VSEN", "pin_description": "输出电压感测 (Output voltage sense)"}, {"pin_number": "11", "pin_name": "FBG", "pin_description": "输出电压感测的反馈地 (Feedback ground for output voltage sense.)"}, {"pin_number": "12", "pin_name": "CSN", "pin_description": "电流感测负输入 (Current sense negative input.)"}, {"pin_number": "13", "pin_name": "CSP", "pin_description": "电流感测正输入 (Current sense positive input)"}, {"pin_number": "14", "pin_name": "OSC", "pin_description": "频率编程输入 (Frequency programming input.)"}, {"pin_number": "15", "pin_name": "VCC", "pin_description": "模拟控制电路的电源输入 (Supply input for analog control circuitry.)"}, {"pin_number": "16", "pin_name": "GND", "pin_description": "逻辑地和低侧栅极驱动返回 (Logic ground and low-side gate drive return.)"}]}], "datasheet_cn": {"datasheet_name": "ZHCS475A", "datasheet_path": "用户上传文件", "release_date": "2012-11-01", "version": "A"}, "datasheet_en": {"datasheet_name": "SLUSAA9", "datasheet_path": "未找到", "release_date": "未找到", "version": "未找到"}, "family_comparison": "未找到", "power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 19, "min_input_voltage": 1.5, "max_output_voltage": "0.7 * VIN", "min_output_voltage": 0.8, "max_output_current": "依赖外部元件", "max_switch_frequency": 1, "quiescent_current": 60, "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "20 mV / 30 mV (DCR sensing)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "不适用", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy_percentage": 0.5, "output_reference_voltage_value": 0.8, "loop_control_mode": "电压模式", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "19V", "min_input_voltage": "1.5V", "max_output_voltage": "0.7 * VIN", "min_output_voltage": "0.8V", "max_output_current": "依赖外部元件", "max_switch_frequency": "1MHz", "quiescent_current": "60uA", "high_side_mosfet_resistance": "不适用(控制器)", "low_side_mosfet_resistance": "不适用(控制器)", "over_current_protection_threshold": "20mV (计数器) / 30mV (立即关断)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "跳跃模式 (Skip Mode)", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±0.5%", "output_reference_voltage": "0.8V", "loop_control_mode": "电压模式"}}