{"part_number": "MP4350", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压", "part_number_title": "2.5A, 4MHz, 20V Step-Down Converter", "features": ["100µA Quiescent Current", "Wide 4.5V to 20V Operating Input Range", "150mΩ Internal Power MOSFET", "Up to 4MHz Programmable Switching Frequency", "Ceramic Capacitor Stable", "Internal Soft-Start", "Internal Set Current Limit without a Current Sensing Resistor", "Up to 95% Efficiency", "Output Adjustable from 0.8V to 16V", "Available in a 10-Pin QFN (3mm x 3mm) Package"], "description": "The MP4350 is a high frequency step-down switching regulator with an integrated internal high-side high voltage power MOSFET. It provides 2.5A output with current mode control for fast loop response and easy compensation. The wide 4.5V to 20V input range accommodates a variety of step-down applications, including those in an automotive input environment. A 100μA operational quiescent current allows use in battery-powered applications. High power conversion efficiency over a wide load range is achieved by scaling down the switching frequency at light load condition to reduce the switching and gate driving losses. The frequency foldback helps prevent inductor current runaway during startup and thermal shutdown provides reliable, fault tolerant operation. By switching at 4MHz, the MP4350 is able to prevent switching related EMI (Electromagnetic Interference) noise problems, such as those found in AM radio and ADSL applications. The MP4350 is available in a small 3mm x 3mm 10-pin QFN package.", "applications": ["DSL Power", "Set Top Boxes", "Battery Powered Systems"], "ordering_information": [{"part_number": "MP4350", "order_device": "MP4350DQ", "status": "Active", "package_type": "QFN10", "package_code": "QFN10 (3x3)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-229, VARIATION VEED-5", "marking": "M8YW", "pin_count": 10, "length": 3.0, "width": 3.0, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "MP4350", "package_type": "QFN10", "pins": [{"pin_number": "1, 2", "pin_name": "SW", "pin_description": "Switch Node. This is the output from the high-side switch. A low forward drop <PERSON><PERSON><PERSON>ky diode to ground is required. The diode must be close to the SW pins to reduce switching spikes."}, {"pin_number": "3", "pin_name": "EN", "pin_description": "Enable Input. Pulling this pin below the specified threshold shuts the chip down. Pulling it up above the specified threshold or leaving it floating enables the chip."}, {"pin_number": "4", "pin_name": "COMP", "pin_description": "Compensation. This node is the output of the error amplifier. Control loop frequency compensation is applied to this pin."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Feedback. This is the input to the error amplifier. An external resistive divider connected between the output and GND which scales down VOUT equal to the internal +0.8V reference."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Ground. It should be connected as close as possible to the output capacitor to shorten the high current switch paths."}, {"pin_number": "7", "pin_name": "FREQ", "pin_description": "Switching Frequency Program Input. Connect a resistor from this pin to ground to set the switching frequency."}, {"pin_number": "8, 9", "pin_name": "VIN", "pin_description": "Input Supply. This supplies power to all the internal control circuitry, including the bootstrap regulator and the high-side switch. A decoupling capacitor to ground must be placed close to this pin to minimize switching spikes."}, {"pin_number": "10", "pin_name": "BST", "pin_description": "Bootstrap. This is the positive power supply for the internal floating high-side MOSFET driver. Connect a bypass capacitor between this pin and SW pin."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MP4350", "datasheet_path": "用户上传", "release_date": "2020-01-21", "version": "1.0"}, "family_comparison": "未找到", "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 20, "min_input_voltage": 4.5, "max_output_voltage": 16, "min_output_voltage": 0.8, "max_output_current": 2.5, "max_switch_frequency": 4, "quiescent_current": 100, "high_side_mosfet_resistance": 150, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 3.5, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 3.0, "output_reference_voltage": 0.8, "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "20V", "min_input_voltage": "4.5V", "max_output_voltage": "16V", "min_output_voltage": "0.8V", "max_output_current": "2.5A", "max_switch_frequency": "4MHz", "quiescent_current": "100µA", "high_side_mosfet_resistance": "150mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "3.5A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "频率折返", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±3%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}