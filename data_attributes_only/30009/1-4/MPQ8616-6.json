{"part_number": "MPQ8616-6", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND (Not Recommended for New Designs)", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "High Efficiency, 6A/12A, 6V Synchronous Step-down Converter", "features": ["1.5V to 6V Wide Input Range", "3 V to 6V VCC Operating Supply", "6A/12A Output Current", "Low RDS(ON) Internal Power MOSFETs", "Proprietary Switching Loss Reduction Technique", "Adaptive COT for Ultrafast Transient Response", "1% Reference Voltage Over -20°C to +85°C Junction Temperature Range", "Programmable Soft Start Time", "Pre-Bias Start up", "Programmable Switching Frequency from 300kHz to 1MHz.", "Minimum On Time TON_MIN=60ns", "Minimum Off Time TOFF_MIN=100ns", "Non-latch OCP, non-latch OVP Protection and Thermal Shutdown", "Output Adjustable from 0.61V to 4.5V"], "description": "The MPQ8616 is fully integrated high frequency synchronous rectified step-down switch mode converter. It offers very compact solutions to achieve 6A/12A output current from a 3V to 6V input with excellent load and line regulation. Constant-On-Time (COT) control mode provides fast transient response and eases loop stabilization. The MPQ8616 can operate with a low-cost electrolytic capacitor and can support ceramic output capacitor with external slope compensation. Operating frequency is programmed by an external resistor and is compensated for variations in VIN. It is almost constant with all the input voltage and output load conditions. Under voltage lockout is internally set at 2.8 V, but can be increased by programming the threshold with a resistor network on the enable pin. The output voltage startup ramp is controlled by the soft start pin. A power good signal indicates the output is within its nominal voltage range. Full fault protection including OCP, SCP, OVP UVP and OTP is provided by internal comparators. The MPQ8616 requires a minimum number of readily available standard external components and are available in QFN3x4 packages.", "applications": ["Telecom System Base Stations", "Networking Systems", "Server", "Personal Video Recorders", "Flat Panel Television and Monitors", "Distributed Power Systems"], "ordering_information": [{"part_number": "MPQ8616-6", "order_device": "MPQ8616GL-6-Z", "status": "NRFND (Not Recommended for New Designs)", "package_type": "QFN", "package_code": "QFN (3x4mm)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "MP8616 6", "pin_count": 14, "length": 4, "width": 3, "height": "未找到", "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MPQ8616", "package_type": "QFN14 (3x4mm)", "pins": [{"pin_number": "1", "pin_name": "AGND", "pin_description": "Analog ground."}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Feedback. An external resistor divider from the output to GND, tapped to the FB pin, sets the output voltage. It is recommended to place the resistor divider as close to FB pin as possible. Vias should be avoided on the FB traces."}, {"pin_number": "3", "pin_name": "SS", "pin_description": "Soft Start. Connect on external capacitor to program the soft start time for the switch mode regulator."}, {"pin_number": "4", "pin_name": "EN", "pin_description": "Enable pin. Pull this pin higher than 1.8V to enable the chip. For automatic start-up, connect EN pin to VIN with 100KΩ resistor. Can be used to set the on/off threshold (adjust UVLO) with two additional resistors. When the nominal VOUT is higher than 2.5V, EN should be pulled low before VIN powers off."}, {"pin_number": "5", "pin_name": "VCC", "pin_description": "External bias supply voltage for driver and control circuits. For 1.5V to 3V input application, provide VCC with separate 3.3V/5V bias supply. For 3V to 6V input application, provide VCC with separate 3.3V/5V bias supply or tie VCC to VIN with 10ohm resistor. Decouple with a minimum 4.7µF ceramic capacitor as close to the pin as possible. X7R or X5R grade dielectric ceramic capacitors are recommended for their stable temperature characteristics."}, {"pin_number": "6", "pin_name": "PG", "pin_description": "Power good output. It is high if the output voltage is higher than 90% of the nominal voltage. There is a delay from FB ≥ 90% to PG goes high."}, {"pin_number": "7", "pin_name": "BST", "pin_description": "Bootstrap. A capacitor connected between SW and BS pins is required to form a floating supply across the high-side switch driver."}, {"pin_number": "8-9", "pin_name": "GND", "pin_description": "System Ground. This pin is the reference ground of the regulated output voltage. For this reason care must be taken in PCB layout."}, {"pin_number": "10-11", "pin_name": "IN", "pin_description": "Supply Voltage. The IN pin supplies power for internal MOSFET and regulator. The MPQ8616 operate from a +3V to +6V input rail. An input capacitor is needed to decouple the input rail. Use wide PCB traces and multiple vias to make the connection."}, {"pin_number": "12", "pin_name": "FREQ", "pin_description": "Frequency setting pin. A resistor connected between FREQ and IN is required to set the switching frequency. The ON time is determined by the input voltage and the resistor connected to the FREQ pin. IN connect through a resistor is used for line feed-forward and makes the frequency basically constant during input voltage's variation. Recommend a 10pF decoupling capacitor from FREQ to GND."}, {"pin_number": "13-14", "pin_name": "SW", "pin_description": "Switch Output. Connect this pin to the inductor and bootstrap capacitor. This pin is driven up to the VIN voltage by the high-side switch during the on-time of the PWM duty cycle. The inductor current drives the SW pin negative during the off-time. The on-resistance of the low-side switch and the internal Schottky diode fixes the negative voltage. Use wide PCB traces to make the connection."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "MPQ8616 Rev. 1.2", "path": "current_file", "release_date": "2019-01-09", "version": "1.2"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "6V", "min_input_voltage": "1.5V", "max_output_voltage": "4.5V", "min_output_voltage": "0.61V", "max_output_current": "6A", "max_switch_frequency": "1MHz", "quiescent_current": "1050µA", "high_side_mosfet_resistance": "19.8mΩ", "low_side_mosfet_resistance": "15.3mΩ", "over_current_protection_threshold": "12A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "FCCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.61V", "loop_control_mode": "固定导通时间控制"}}