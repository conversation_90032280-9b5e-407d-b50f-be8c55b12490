{"part_number": "TPS6286A06", "manufacturer": "Texas Instruments", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS6286Axx 和 TPS6286Bxx 采用 2mm × 3mm QFN 封装且具有 FB/I2C 接口的 2.4V 至 5.5V 输入、6A、8A 和 10A 同步降压转换器", "features": ["输入电压范围为 2.4V 至 5.5V", "8mΩ 和 8mΩ 内部功率 MOSFET", "5.1µA 运行静态电流", "1.2MHz 开关频率", "100% 占空比模式", "0.7% 的输出电压精度", "强制 PWM 或省电模式", "DCS-Control 拓扑 (恒定导通时间)", "FB 版本 (TPS6286Axx) 输出电压: 0.6V 至 Vin 可调电压, 0.4V 至 1.6V 固定电压 (通过外部电阻器)", "I2C 版本 (TPS6286Bxx), 具有可选的: 输出电压范围为 0.4V 至 1.675V, 步长为 5mV, 省电模式或强制 PWM 模式, 断续或锁存短路保护, 动态自适应电压调节斜坡速度", "输出电压放电", "断续短路保护", "具有窗口比较器的电源正常状态指示器", "热关断", "可调软启动", "-40°C 至 125°C 工作温度范围", "使用 TPS6286Axx 和 TPS6286Bxx 并借助 WEBENCH® Power Designer 创建定制设计方案"], "description": "TPS6286Axx 和 TPS6286Bxx 器件是高频同步降压转换器,可提供高效灵活的高功率密度设计。这些转换器在中高负载条件下以脉宽调制 (PWM) 模式运行,并在轻负载时自动进入省电模式运行,从而在整个负载电流范围内保持高效率。这些器件还可强制进入 PWM 模式运行,尽量减少输出电压纹波。凭借 DCS-Control 架构,这些器件可提供出色的负载瞬态性能和严格的输出电压精度。这些器件具有电源正常信号和可调软启动特性,能够以 100% 模式运行。在故障保护方面,这些器件集成了断续短路保护以及热关断功能。", "applications": ["适用于 FPGA、CPU、ASIC 和 DSP 的内核电源", "机器视觉摄像机", "IP 网络摄像头", "固态硬盘"], "ordering_information": [{"part_number": "TPS6286A06", "order_device": "TPS6286A06VBMR", "status": "Active", "package_type": "VQFN-HR", "package_code": "VBM", "carrier_description": "LARGE T&R", "carrier_quantity": 3000, "package_drawing_code": "VBM0013A", "marking": "A06", "pin_count": 13, "length": 3.0, "width": 2.0, "height": 1.0, "pitch": "未找到", "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "可调", "application_grade": "Automotive"}], "typical_application_circuit": "Page 1, 典型应用原理图", "pin_config": "Page 4, 图 5-1. 用于 FB 版本的 13 引脚 VBM VQFN-HR 封装 (顶视图)", "function_block_diagram": "Page 13, 图 7-1. 功能方框图 - FB 版本 (TPS6286Axx)", "pin_function": [{"product_part_number": "TPS6286Axx", "package_type": "VBM VQFN-HR", "pins": [{"pin_number": "5", "pin_name": "EN", "pin_description": "器件使能引脚。要启用器件,必须将此引脚的电平拉高。将这个引脚拉至低电平会禁用器件。不保持悬空。"}, {"pin_number": "3", "pin_name": "FB", "pin_description": "电压反馈输入。将输出电压电阻分压器连接到此引脚。使用固定输出电压时,直接连接到 OUT。"}, {"pin_number": "10, 11, 12", "pin_name": "GND", "pin_description": "电源地引脚"}, {"pin_number": "2", "pin_name": "OUT", "pin_description": "输出电压检测引脚。此引脚必须直接连接到输出电容器。"}, {"pin_number": "7", "pin_name": "PG", "pin_description": "电源正常开漏输出引脚。上拉电阻器可连接到最高 5.5V 的电压。如果未使用该引脚,则将其保持悬空。当器件处于关断状态时,该引脚被拉至 GND。"}, {"pin_number": "4", "pin_name": "SS", "pin_description": "软启动引脚。一个外部电容器可以调节软启动时间。如果未使用,则将该引脚悬空以设置默认 SS 时间。"}, {"pin_number": "1, 9", "pin_name": "SW", "pin_description": "功率级的开关引脚"}, {"pin_number": "8, 13", "pin_name": "VIN", "pin_description": "电源输入电压引脚"}, {"pin_number": "6", "pin_name": "VSET/MODE", "pin_description": "将电阻器连接到 GND 可选择其中一个固定输出电压。将引脚连接到高电平或低电平可选择一个可调输出电压。器件启动后,该引脚作为 MODE 输入运行。施加高电平可选择强制 PWM 模式运行,施加低电平可选择省电模式运行。"}]}], "datasheet_cn": {"name": "ZHCSTJ4B", "path": "ZHCSTJ4B.pdf", "release_date": "2025-03", "version": "B"}, "datasheet_en": {"name": "SLUSFG2", "path": "SLUSFG2.pdf", "release_date": "未找到", "version": "未找到"}, "family_comparison": "Page 3, 表 4 器件选项", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "5.5V", "min_input_voltage": "2.4V", "max_output_voltage": "5.5V", "min_output_voltage": "0.4V", "max_output_current": "6A", "max_switch_frequency": "1.2MHz", "quiescent_current": "5.1µA", "high_side_mosfet_resistance": "8mΩ", "low_side_mosfet_resistance": "8mΩ", "over_current_protection_threshold": "8A", "operation_mode": "同步", "output_voltage_config_method": "可调/固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "Yes", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±0.7%", "output_reference_voltage": "0.6V", "loop_control_mode": "固定导通时间控制"}}