{"part_number": "TPS5615", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压控制器(外部开关)", "part_number_title": "TPS5615, TPS5618, TPS5625, TPS5633 SYNCHRONOUS-BUCK HYSTERETIC REGULATOR CONTROLLER", "features": ["±1% Reference Over Full Operating Temperature Range", "Synchronous Rectifier Driver for >90% Efficiency", "Fixed Output Voltage Options of 1.5 V, 1.8 V, 2.5 V, and 3.3 V", "User-Selectable Hysteretic-Type Control", "Low Supply Current . . . 3 mA Typ", "11.4-V to 13-V Input Voltage Range, Vcc", "Power Good Output", "Programmable Soft-Start", "Overvoltage/Overcurrent Protection", "Active Deadtime Control"], "description": "The TPS5615 family of synchronous-buck regulator controllers provides an accurate supply voltage to DSPs. The output voltage is internally set by a resistive divider with an accuracy of 1% over the full operating temperature range. A hysteretic controller with user-selectable hysteresis is used to dramatically reduce overshoot and undershoot caused by load transients. Propagation delay from the comparator inputs to the output drivers is less than 250 ns. Overcurrent shutdown and crossover protection for the output drivers combine to eliminate destructive faults in the output FETs. PWRGD monitors the output voltage and pulls the open-collector output low when the output drops below 93% of the nominal output voltage. An overvoltage circuit disables the output drivers if the output voltage rises 15% above the nominal value. The inhibit pin can be used to control power sequencing. Inhibit and undervoltage lockout assures that the 12-V supply voltage and system supply voltage (5 V or 3.3 V) are within proper operating limits before the controller starts. The output driver circuits include 2-A drivers with internal 8-V gate-voltage regulators that can easily provide sufficient power for today’s high-powered DSPs. The high-side driver can be configured either as a ground-referenced driver or as a floating bootstrap driver. The TPS5615 family is available in a 28-pin TSSOP PowerPad™ package. It operates over a junction temperature range of 0°C to 125°C.", "applications": ["DSP supplies", "High-powered DSPs"], "ordering_information": [{"part_number": "TPS5615", "order_device": "TPS5615PWP", "status": "Active", "package_type": "TSSOP", "package_code": "PWP", "carrier_description": "<PERSON><PERSON>", "carrier_quantity": "50", "package_drawing_code": "PWP (R-PDSO-G28)", "marking": "未找到", "pin_count": "28", "length": "9.7", "width": "4.4", "height": "1.2", "pitch": "0.65", "min_operation_temp": "0", "max_operation_temp": "125", "output_voltage": "1.5", "application_grade": "Industrial"}, {"part_number": "TPS5615", "order_device": "TPS5615PWPR", "status": "Active", "package_type": "TSSOP", "package_code": "PWP", "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": "未找到", "package_drawing_code": "PWP (R-PDSO-G28)", "marking": "未找到", "pin_count": "28", "length": "9.7", "width": "4.4", "height": "1.2", "pitch": "0.65", "min_operation_temp": "0", "max_operation_temp": "125", "output_voltage": "1.5", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS5615, TPS5618, TPS5625, TPS5633", "package_type": "PWP", "pins": [{"pin_number": "1", "pin_name": "IOUT", "pin_description": "Current out. Output voltage on this terminal is proportional to the load current as measured across the Rds(on) of the high side FET. The voltage on this terminal equals 2 × RDS(ON) × IOUT. In applications where very accurate current-sensing is required, a sense resistor should be connected between the input supply and the drain of the high-side FETs."}, {"pin_number": "2", "pin_name": "AGND2", "pin_description": "Analog ground (must be connected)."}, {"pin_number": "3", "pin_name": "OCP", "pin_description": "Over current protection. Current limit trip point is set with a resistor divider between IOUT and ANAGND."}, {"pin_number": "4", "pin_name": "VHYST", "pin_description": "Hysteresis set input. The hysteresis is set with a resistor divider from VREFB to ANAGND. Hysteresis = 2 × (VREFB – VHYST)"}, {"pin_number": "5", "pin_name": "VREFB", "pin_description": "Buffered reference voltage"}, {"pin_number": "6", "pin_name": "VSENSE", "pin_description": "Voltage sense Input. To be connected from converter output voltage bus to sense and control output voltage. It is recommended that a RC low-pass filter be connected at this pin to filter noise."}, {"pin_number": "7", "pin_name": "ANAGND", "pin_description": "Analog ground"}, {"pin_number": "8", "pin_name": "SLOWST", "pin_description": "Slow Start (soft start). A capacitor form SLOWST to ANAGND sets the slowstart time. Slowstart current = IVREFB/5"}, {"pin_number": "9", "pin_name": "BIAS", "pin_description": "Analog bias pin. A 1-µF capacitor should be connected from BIAS to ANAGND."}, {"pin_number": "10", "pin_name": "LODRV", "pin_description": "Low drive enable. Normally tied to 5 V. To configure the low-side FET as a crowbar, pull LODRV low."}, {"pin_number": "11", "pin_name": "LOHIB", "pin_description": "Low side inhibit. Connect to the junction of the high and low-side FETs to control the anti-cross-conduction and eliminate shoot-through current. Disabled when configured in crowbar mode."}, {"pin_number": "12", "pin_name": "DRVGND", "pin_description": "Drive ground. Ground for FET drivers. Connect to FET PWRGND."}, {"pin_number": "13", "pin_name": "LOWDR", "pin_description": "Low drive. Output drive to synchronous rectifier FETs."}, {"pin_number": "14", "pin_name": "DRV", "pin_description": "Drive regulator for the FET drivers. A 1-µF capacitor should be connected from DRV to DRVGND."}, {"pin_number": "15", "pin_name": "Vcc", "pin_description": "12-V supply. A 1-µF capacitor should be connected from Vcc to DRVGND."}, {"pin_number": "16", "pin_name": "BOOT", "pin_description": "Bootstrap. A 1-µF capacitor should be connected from BOOT to BOOTLO."}, {"pin_number": "17", "pin_name": "HIGHDR", "pin_description": "High drive. Output drive to high-side power switching FETs."}, {"pin_number": "18", "pin_name": "BOOTLO", "pin_description": "Bootstrap low. Connect to the junction of the high-side and low-side FETs for floating drive configuration. Connect to PGND for ground-reference drive configuration."}, {"pin_number": "19", "pin_name": "HISENSE", "pin_description": "High current sense. For current sensing across high-side FETs, connect to the drain of the high-side FETs; for optional current sensing scheme, connect to power supply side of current-sense resistor placed in series with high-side FET drain."}, {"pin_number": "20", "pin_name": "LOSENSE", "pin_description": "Low current sense. For current sensing across high-side FETs, connect to the source of the high-side FETs; for optional current sensing scheme, connect to high-side FET drain side of current-sense resistor placed in series with high-side FET drain."}, {"pin_number": "21", "pin_name": "IOUTLO", "pin_description": "Current sense low output. This is the voltage on the LOSENSE terminal when the high-side FETs are on. A ceramic capacitor (between 0.033 µF and 0.1 µF) should be connected from IOUTLO to HISENSE to hold the sensed voltage."}, {"pin_number": "22", "pin_name": "INHIBIT", "pin_description": "Disables the drive signals to the MOSFET drivers. Also serves as UVLO for system logic supply (3.3 V or 5 V). An external pullup resistor should be connected to system-logic supply."}, {"pin_number": "23-27", "pin_name": "NC", "pin_description": "No connect"}, {"pin_number": "28", "pin_name": "PWRGD", "pin_description": "Power good. PWRGD signal goes high when output voltage is within 7% of voltage setpoint. Open-drain output."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "TPS5615, TPS5618, TPS5625, TPS5633", "datasheet_path": "用户上传", "release_date": "2000-07-01", "version": "SLVS177B"}, "family_comparison": "The TPS56xx family consists of four fixed-output voltage controllers. The primary difference is the nominal output voltage: TPS5615 (1.5V), TPS5618 (1.8V), TPS5625 (2.5V), and TPS5633 (3.3V). All share the same package, pinout, and core features.", "power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "13", "min_input_voltage": "11.4", "max_output_voltage": "1.5", "min_output_voltage": "1.5", "max_output_current": "8", "max_switch_frequency": "未找到", "quiescent_current": "3", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "Externally Adjustable", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Hysteretic", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy_percentage": "1", "output_reference_voltage_value": "1.5", "loop_control_mode": "迟滞模式控制", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "13V", "min_input_voltage": "11.4V", "max_output_voltage": "1.5V", "min_output_voltage": "1.5V", "max_output_current": "8A", "max_switch_frequency": "Variable (Hysteretic)", "quiescent_current": "3mA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "Externally Adjustable", "operation_mode": "同步", "output_voltage_config_method": "固定", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Hysteretic", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "No", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "1.5V", "loop_control_mode": "迟滞模式控制"}}