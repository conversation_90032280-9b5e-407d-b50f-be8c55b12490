{"part_number": "MPQ4415M", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压(<PERSON>)", "part_number_title": "MPQ4415M 1.5A, 36V, 2.2MHz, High-Efficiency, Synchronous, Step-Down Converter AEC-Q100 Qualified", "features": ["EMI Reduction Technique", "Wide 4V to 36V Operating Input Range", "1.5A Continuous Load Current", "90mΩ/50mΩ Low RDS(ON) Internal Power MOSFETS", "High-Efficiency Synchronous Mode Operation", "Default 2.2MHz Switching Frequency", "450kHz to 2.2MHz Frequency Sync", "Forced Continuous Conduction Mode (CCM)", "Internal Soft Start (SS)", "Power Good (PG) Indicator", "Over-Current Protection (OCP) with Valley-Current Detection and Hiccup", "Thermal Shutdown", "Output Adjustable from 0.8V", "Available in a QFN-13 (2.5mmx3mm) Package", "CISPR25 Class 5 Compliant", "Available in a Wettable Flank Package", "Available in AEC-Q100 Grade 1"], "description": "The MPQ4415M is a high-frequency, synchronous, rectified, step-down, switch-mode converter with built-in power MOSFETs. The MPQ4415M offers a very compact solution that achieves 1.5A of continuous output current with excellent load and line regulation over a wide input supply range. The MPQ4415M uses synchronous mode operation for higher efficiency over the output current load range. Current-mode operation provides fast transient response and eases loop stabilization. Full protection features include over-current protection (OCP) and thermal shutdown. The MPQ4415M requires a minimal number of readily available, standard, external components and is available in a space-saving QFN-13 (2.5mmx3mm) package.", "applications": ["Automotive", "Industrial Control Systems", "Medical and Imaging Equipment", "Telecom Applications", "Distributed Power Systems"], "ordering_information": [{"part_number": "MPQ4415M", "order_device": "MPQ4415MGQB", "status": "Active", "package_type": "QFN-13", "package_code": "QFN-13 (2.5mmx3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "ANM", "pin_count": "13", "length": "3.0", "width": "2.5", "height": "1.0", "pitch": "0.65", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "未找到"}, {"part_number": "MPQ4415M", "order_device": "MPQ4415MGQB-AEC1", "status": "Active", "package_type": "QFN-13", "package_code": "QFN-13 (2.5mmx3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "ANM", "pin_count": "13", "length": "3.0", "width": "2.5", "height": "1.0", "pitch": "0.65", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Automotive"}, {"part_number": "MPQ4415M", "order_device": "MPQ4415MGQBE-AEC1", "status": "Active", "package_type": "QFN-13", "package_code": "QFN-13 (2.5mmx3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "AXR", "pin_count": "13", "length": "3.0", "width": "2.5", "height": "1.0", "pitch": "0.65", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MPQ4415M", "package_type": "QFN-13", "pins": [{"pin_number": "1, 2", "pin_name": "IN", "pin_description": "Supply voltage. IN supplies power for the internal MOSFET and regulator. The MPQ4415M operates from a 4V to 36V input rail. A low ESR and low-inductance capacitor is required to decouple the input rail. Place the input capacitor very close to IN and connect it with wide PCB traces and multiple vias."}, {"pin_number": "3", "pin_name": "NC", "pin_description": "No connection. Do not connect."}, {"pin_number": "4", "pin_name": "PG", "pin_description": "Power good indicator. The output of PG is an open drain and goes high if the output voltage exceeds 88% of the nominal voltage."}, {"pin_number": "5", "pin_name": "EN/SYNC", "pin_description": "Enable/synchronize. Pull EN/SYNC high to enable the MPQ4415M. Float EN/SYNC or connect EN/SYNC to ground to disable the MPQ4415M. If an external sync clock is applied to EN/SYNC, the internal clock follows the sync frequency."}, {"pin_number": "6", "pin_name": "FB", "pin_description": "Feedback. Connect FB to the tap of an external resistor divider from the output to AGND to set the output voltage. The frequency foldback comparator lowers the oscillator frequency when the FB voltage is below 400mV to prevent current limit runaway during a short-circuit fault. Place the resistor divider as close to FB as possible. Avoid placing vias on the FB traces."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "Internal bias supply. Decouple VCC with a 0.1μF - 0.22μF capacitor. The capacitance should be no more than 0.22μF."}, {"pin_number": "8", "pin_name": "AGND", "pin_description": "Analog ground. AGND is the reference ground of the logic circuit. AGND is connected to PGND internally. There is no need to add external connections to PGND."}, {"pin_number": "9", "pin_name": "SW", "pin_description": "Switch output. Connect SW using a wide PCB trace."}, {"pin_number": "10", "pin_name": "BST", "pin_description": "Bootstrap. A capacitor connected between SW and BST is required to form a floating supply across the high-side switch driver. A 20Ω resistor placed between the SW and BST capacitor is strongly recommended to reduce SW voltage spikes."}, {"pin_number": "11, 12, 13", "pin_name": "PGND", "pin_description": "Power ground. PGND is the reference ground of the power device and requires careful consideration during PCB layout. For best results, connect PGND with copper pours and vias."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "MPQ4415M", "path": "MPQ4415M.pdf", "release_date": "2017-09-30", "version": "1.01"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "36", "min_input_voltage": "4", "max_output_voltage": "Vin*DMAX", "min_output_voltage": "0.8", "max_output_current": "1.5", "max_switch_frequency": "2.6", "quiescent_current": "600", "high_side_mosfet_resistance": "90", "low_side_mosfet_resistance": "50", "over_current_protection_threshold": "4", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "未找到", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "未找到", "output_discharge": "未找到", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "未找到", "dynamic_voltage_setting": "未找到", "output_voltage_accuracy": "2.1", "output_reference_voltage": "0.807", "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "36V", "min_input_voltage": "4V", "max_output_voltage": "Vin*DMAX", "min_output_voltage": "0.8V", "max_output_current": "1.5A", "max_switch_frequency": "2.6MHz", "quiescent_current": "600µA", "high_side_mosfet_resistance": "90mΩ", "low_side_mosfet_resistance": "50mΩ", "over_current_protection_threshold": "4A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "CCM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "未找到", "output_under_voltage_protection": "未找到", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "未找到", "output_discharge": "未找到", "integrated_ldo": "未找到", "frequency_synchronization": "Yes", "output_voltage_tracking": "未找到", "dynamic_voltage_setting": "未找到", "output_voltage_accuracy": "1.5%", "output_reference_voltage": "0.807V", "loop_control_mode": "峰值电流模式"}}