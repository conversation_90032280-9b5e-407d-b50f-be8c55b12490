{"part_number": "MP28373", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片", "part_number_title": "3A, 28V, 1.4MHz Step-Down Converter", "features": ["3A Continuous Output Current, 4A Peak Output Current", "Programmable Soft-Start", "100mΩ Internal Power MOSFET Switch", "Stable with Low ESR Output Ceramic Capacitors", "Up to 91% Efficiency", "20μA Shutdown Mode", "Fixed 1.4MHz Frequency", "Thermal Shutdown", "Cycle-by-Cycle Over Current Protection", "Wide 4.75V to 28V Operating Input Range", "Output is Adjustable From 0.92V to 21V", "Under Voltage Lockout"], "description": "The MP28373 is a 1.4MHz step-down regulator with a built-in power MOSFET. It achieves 3A continuous output current over a wide input supply range with excellent load and line regulation. Current mode operation provides fast transient response and eases loop stabilization. Fault condition protection includes cycle-by-cycle current limiting and thermal shutdown. Adjustable soft-start reduces the stress on the input source at turn-on. In shutdown mode, the regulator draws 20μA of supply current. The MP28373 is available in an 8-pin SOIC package with an exposed pad, and requires a minimum number of readily available external components to complete a 3A step-down DC to DC converter solution.", "applications": ["Distributed Power Systems", "Battery Chargers", "Pre-Regulator for Linear Regulators"], "ordering_information": [{"part_number": "MP28373", "order_device": "MP28373DN", "status": "NRFND", "package_type": "SOIC8N", "package_code": "SOIC8N", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "SOIC8N (EXPOSED PAD)", "marking": "未找到", "pin_count": "8", "length": "5.00", "width": "4.00", "height": "1.70", "pitch": "1.27", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": true, "pin_config": true, "function_block_diagram": true, "pin_function": [{"product_part_number": "MP28373", "package_type": "SOIC8N", "pins": [{"pin_number": "1", "pin_name": "BS", "pin_description": "High-Side Gate Drive Boost Input. BS supplies the drive for the high-side N-Channel MOSFET switch. Connect a 10nF or greater capacitor from SW to BS to power the high side switch."}, {"pin_number": "2", "pin_name": "IN", "pin_description": "Power Input. IN supplies the power to the IC, as well as the step-down converter switches. Drive IN with a 4.75V to 28V power source. Bypass IN to GND with a suitably large capacitor to eliminate noise on the input to the IC."}, {"pin_number": "3", "pin_name": "SW", "pin_description": "Power Switching Output. SW is the switching node that supplies power to the output. Connect the output LC filter from SW to the output load. Note that a capacitor is required from SW to BS to power the high-side switch."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground. Connect the exposed pad on backside to Pin 4."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Feedback Input. FB senses the output voltage to regulate said voltage. Drive FB with a resistive voltage divider from the output voltage. The feedback threshold is 0.92V."}, {"pin_number": "6", "pin_name": "COMP", "pin_description": "Compensation Node. COMP is used to compensate the regulation control loop. Connect a series RC network from COMP to GND to compensate the regulation control loop. In some cases, an additional capacitor from COMP to GND is required."}, {"pin_number": "7", "pin_name": "EN", "pin_description": "Enable Input. EN is a digital input that turns the regulator on or off. Drive EN higher than 2.9V to turn on the regulator, lower than 0.9V to turn it off. For automatic startup, leave EN unconnected."}, {"pin_number": "8", "pin_name": "SS", "pin_description": "Soft-Start Control Input. SS controls the soft start period. Connect a capacitor from SS to GND to set the soft-start period. A 0.1μF capacitor sets the soft-start period to 10ms."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "MP28373", "path": "用户上传的PDF", "release_date": "2014-02-18", "version": "1.01"}, "family_comparison": false, "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 28, "min_input_voltage": 4.75, "max_output_voltage": 21, "min_output_voltage": 0.92, "max_output_current": 3, "max_switch_frequency": 1.4, "quiescent_current": 1.3, "high_side_mosfet_resistance": 100, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 6.5, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 2.1, "output_reference_voltage": 0.92, "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "28V", "min_input_voltage": "4.75V", "max_output_voltage": "21V", "min_output_voltage": "0.92V", "max_output_current": "3A", "max_switch_frequency": "1.4MHz", "quiescent_current": "1300µA", "high_side_mosfet_resistance": "100mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "6.5A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.1%", "output_reference_voltage": "0.92V", "loop_control_mode": "峰值电流模式"}}