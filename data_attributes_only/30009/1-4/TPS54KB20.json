{"part_number": "TPS54KB20", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS54KB2x 具有遥感功能的 4V 至 16V 输入、25A D-CAP4 同步降压转换器", "features": ["输入电压范围为 4V 至 16V", "支持 3.1V 至 5.3V 外部 VCC 辅助电源", "5.8mΩ 和 2.3mΩ MOSFET", "25A 持续输出电流", "针对效率和热性能优化的 16 引脚 WQFN-HR 封装", "Tj = -40°C 至 +125℃ 时为 ±0.5% 基准 (VREF)", "输出电压范围为 VREF 至 5.5V", "差分遥感", "D-CAP4 模式可提供超快负载阶跃响应", "支持所有陶瓷输出电容器", "可选自动跳跃 Eco-mode，用以实现高轻负载效率", "通过 RILIM 实现可编程电流限制", "可选开关频率: 800kHz、1.1MHz、1.4MHz", "可编程软启动时间", "预偏置启动功能", "开漏电源正常状态输出", "谷值过流限制保护", "过压和欠压故障保护", "引脚与 30A TPS54KC23 兼容"], "description": "TPS54KB2x 器件是一款具有自适应导通时间 D-CAP4 控制模式的高效率、小尺寸同步降压转换器。该控制方法无需外部补偿网络，即可在整个输出电压范围内提供较小的最短导通时间和快速负载瞬态响应。该器件不需要外部补偿，因此易于使用并且仅需要很少的外部元件。该器件非常适合空间受限的数据中心应用。TPS54KB2x 器件具有差分遥感功能和高性能集成 MOSFET，在额定工作结温范围具有高精度 (±0.5%) 电压基准。该器件具有精确的负载调整率和线性调整率、Eco-mode 或 FCCM 工作模式、可通过 MSEL 引脚编程的设置以及可编程软启动功能。TPS54KB2x 是一款无铅器件，符合 RoHS 标准，无需豁免。", "applications": ["机架式服务器和刀片式服务器", "硬件加速卡和插件卡", "数据中心交换机", "工业 PC", "基带单元 (BBU)"], "ordering_information": [{"part_number": "TPS54KB20", "order_device": "TPS54KB20RZRR", "status": "Active", "package_type": "WQFN-FCRLF", "package_code": "RZR", "carrier_description": "LARGE T&R", "carrier_quantity": 5000, "package_drawing_code": "RZR0016A", "marking": "T54KB0", "pin_count": 16, "length": 3.0, "width": 3.5, "height": 0.7, "min_operation_temp": -40, "max_operation_temp": 125}, {"part_number": "TPS54KB20", "order_device": "TPS54KB20RZRR.A", "status": "Active", "package_type": "WQFN-FCRLF", "package_code": "RZR", "carrier_description": "LARGE T&R", "carrier_quantity": 5000, "package_drawing_code": "RZR0016A", "marking": "T54KB0", "pin_count": 16, "length": 3.0, "width": 3.5, "height": 0.7, "min_operation_temp": -40, "max_operation_temp": 125}], "typical_application_circuit": "Page 27, 图 6-8", "pin_config": "Page 3, 图 4-1, 图 4-2", "function_block_diagram": "Page 15, 图 6-2", "pin_function": [{"product_part_number": "TPS54KB2x", "package_type": "WQFN-FCRLF", "pins": [{"pin_number": "1", "pin_name": "EN", "pin_description": "使能引脚。使能引脚可开启或关闭直流/直流开关转换器。在启动前将 EN 引脚悬空会禁用转换器。EN 引脚上施加的最大建议电压为 5.5V。TI 不建议将 EN 引脚直接连接到 VIN 引脚。"}, {"pin_number": "2", "pin_name": "AGND", "pin_description": "内部控制电路的模拟地回路和基准。"}, {"pin_number": "3, 9", "pin_name": "VIN", "pin_description": "功率级 MOSFET 和内部 LDO 的电源输入引脚。应将 VIN 引脚和 PGND 引脚之间的去耦输入电容器尽可能靠近放置。需要靠近 IC 在每个 VIN 和 PGND 之间连接一个电容器。"}, {"pin_number": "4, 8, 16", "pin_name": "PGND", "pin_description": "功率级接地回路。此引脚在内部连接到低侧 MOSFET 的源极。在 PGND 引脚下方放置尽可能多的过孔，并尽可能靠近 PGND 引脚。此操作可以更大限度减小寄生阻抗并降低热阻。"}, {"pin_number": "5", "pin_name": "BOOT", "pin_description": "内部高侧 MOSFET 栅极驱动器（升压端子）的电源。从该引脚到 SW 节点之间连接自举电容器。"}, {"pin_number": "6", "pin_name": "SW", "pin_description": "电源转换器的输出开关端子。将该引脚连接到输出电感器。"}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "内部 3V LDO 输出。可将 3.3V 或 5V 的外部辅助电源连接到该引脚以减少内部 LDO 上的功率损耗。该引脚上的电压源为内部电路和栅极驱动器供电。从 VCC 引脚到 PGND 之间连接一个额定电压 > 6.3V 的 1µF 陶瓷电容器进行旁路。将此电容器尽可能靠近 VCC 和 PGND 引脚放置。"}, {"pin_number": "10", "pin_name": "PG", "pin_description": "开漏电源正常状态信号。将外部上拉电阻器连接到电压源。当 FB 电压超出指定限值时，PG 在指定的延迟后变为低电平。"}, {"pin_number": "11", "pin_name": "MSEL", "pin_description": "多功能选择引脚。从 MSEL 引脚到 AGND 的电阻器用于选择强制连续导通模式 (FCCM) 或跳跃模式运行、工作频率和 PWM 斜坡设置。要求使用容差为 ±1% 的电阻。"}, {"pin_number": "12", "pin_name": "ILIM", "pin_description": "电流限值设置引脚。将一个电阻连接到 AGND 即可设置电流限值跳变点。TI 建议使用容差为 ±1% 的电阻。"}, {"pin_number": "13", "pin_name": "GOSNS", "pin_description": "差分遥感电路的负输入端。连接到负载附近的接地检测点。"}, {"pin_number": "14", "pin_name": "FB", "pin_description": "输出电压反馈输入。从输出电压到 GOSNS（抽头至 FB 引脚）的电阻分压器可设置输出电压。将 FB 分压器连接到负载附近的输出电压。"}, {"pin_number": "15", "pin_name": "SS", "pin_description": "将电容器连接到 AGND 以设置 SS 时间。为避免在软启动电容器充电期间发生过冲，该引脚需要一个最小值为 10nF 的电容器。"}]}], "datasheet_cn": "ZHCSPN3A MAY 2023 REVISED FEBRUARY 2024", "datasheet_en": "SLVSGP3", "family_comparison": "Page 1, 器件信息", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 16, "min_input_voltage": 4, "max_output_voltage": 5.5, "min_output_voltage": 0.9, "max_output_current": 25, "max_switch_frequency": 1.4, "quiescent_current": 940, "high_side_mosfet_resistance": 5.8, "low_side_mosfet_resistance": 2.3, "over_current_protection_threshold": "27.5A (谷值, 可编程)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "不适用", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 0.5, "output_reference_voltage": 0.9, "loop_control_mode": "固定导通时间控制", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "4V", "max_output_voltage": "5.5V", "min_output_voltage": "0.9V", "max_output_current": "25A", "max_switch_frequency": "1.4MHz", "quiescent_current": "940µA", "high_side_mosfet_resistance": "5.8mΩ", "low_side_mosfet_resistance": "2.3mΩ", "over_current_protection_threshold": "4.0-27.5A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "跳跃模式", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "闭锁", "output_under_voltage_protection": "闭锁", "output_over_load_protection": "闭锁", "output_short_circuit_protection": "闭锁", "over_temperature_protection": "自动重启", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±0.5%", "output_reference_voltage": "0.9V", "loop_control_mode": "D-CAP4"}}