{"part_number": "TPS548B28", "manufacturer": "Texas Instruments", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压芯片(<PERSON>)", "part_number_title": "具有遥感功能、3V 内部 LDO和断续电流限制功能的 TPS548B28 2.7V至16V 输入、20A 同步降压转换器", "features": ["输入范围为4V 至16V,电流高达20A,无外部偏压", "输入范围为2.7V 至16V,电流高达20A,外部偏压范围为3.13V 至3.6V", "输出电压范围: 0.6V至5.5V", "集成 7.7mΩ 和 2.4mΩ MOSFET,支持 20A 持续输出电流", "在D-CAP3™ 控制模式下可提供超快负载阶跃响应", "支持所有陶瓷输出电容器", "在 - 40℃ 至+125℃ 结温下实现差分遥感,VREF为0.6V ±1%", "可选 FCCM 或自动跳跃 Eco-mode™ 可实现较高的轻负载效率", "通过RTRIP 实现可编程电流限制", "引脚可选开关频率: 600kHz、800kHz、1MHz", "可实现高输出精度的差分遥感功能", "可编程软启动时间", "外部基准输入,用于跟踪", "预偏置启动功能", "开漏电源正常状态输出", "在发生 OC 和UV 故障时进入断续模式,在发生OV 故障时进入闭锁模式", "4mm x 3mm 21 引脚 QFN 封装", "引脚与 15A TPS548A28 兼容", "完全符合RoHS标准,无需豁免"], "description": "TPS548B28 器件是一款具有自适应导通时间 D-CAP3 控制模式的高效率、小尺寸同步降压转换器。该器件不需要外部补偿,因此易于使用并且仅需要很少的外部元件。该器件非常适合空间受限的数据中心应用。TPS548B28 器件具有差分遥感功能和高性能集成 MOSFET,在整个工作结温范围具有高精度(±1%) 0.6V电压基准。该器件具有快速负载瞬态响应、精确负载调节和线路调节、跳跃模式或 FCCM 运行模式以及可编程软启动功能。TPS548B28 是一款无铅器件,完全符合RoHS标准,无需豁免。", "applications": ["机架式服务器和刀片式服务器", "硬件加速卡和插件卡", "数据中心交换机", "工业 PC"], "ordering_information": [{"part_number": "TPS548B28", "order_device": "TPS548B28RWWR", "status": "Active", "package_type": "VQFN-HR", "package_code": "RWW", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RWW0021A", "marking": "T548B8A", "pin_count": "21", "length": "4.00", "width": "3.00", "height": "1.0", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可调", "application_grade": "Industrial"}, {"part_number": "TPS548B28", "order_device": "TPS548B28RWWR.A", "status": "Active", "package_type": "VQFN-HR", "package_code": "RWW", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RWW0021A", "marking": "T548B8A", "pin_count": "21", "length": "4.00", "width": "3.00", "height": "1.0", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可调", "application_grade": "Industrial"}, {"part_number": "TPS548B28", "order_device": "TPS548B28RWWR.B", "status": "Active", "package_type": "VQFN-HR", "package_code": "RWW", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RWW0021A", "marking": "T548B8A", "pin_count": "21", "length": "4.00", "width": "3.00", "height": "1.0", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "可调", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS548B28", "package_type": "VQFN-HR (RWW)", "pins": [{"pin_number": "1", "pin_name": "BOOT", "pin_description": "高侧栅极驱动器的电源轨（升压端）。将自举电容器从此引脚连接到 SW 节点。"}, {"pin_number": "2", "pin_name": "AGND", "pin_description": "接地引脚。内部控制电路的参考点。"}, {"pin_number": "3", "pin_name": "TRIP", "pin_description": "电流限制设置引脚。将一个电阻器连接到 AGND 以设置电流限制跳闸点。强烈建议使用 ±1% 容差的电阻器。"}, {"pin_number": "4", "pin_name": "MODE", "pin_description": "MODE 引脚设置强制连续导通模式 (FCCM) 或跳跃模式操作。它还通过将电阻器从 MODE 引脚连接到 AGND 引脚来选择工作频率。建议使用 ±1% 容差的电阻器。"}, {"pin_number": "5", "pin_name": "SS/REFIN", "pin_description": "双功能引脚。软启动功能：将电容器连接到 VSNS- 引脚可编程软启动时间。最小软启动时间（1.5 ms）在内部固定。此引脚需要至少 1-nF 的电容器，以避免在软启动电容器充电期间出现过冲。REFIN 功能：器件始终将此 SS/REFIN 引脚上的电压视为控制环路的参考。内部参考电压可以通过此引脚上的外部直流电压源覆盖，用于跟踪应用。"}, {"pin_number": "6", "pin_name": "VSNS-", "pin_description": "远程电压感测配置的返回连接。它也用作内部参考的接地。对于单端感测配置，短接到 AGND。"}, {"pin_number": "7", "pin_name": "FB", "pin_description": "输出电压反馈输入。从 VOUT 到 VSNS-（连接到 FB 引脚）的电阻分压器设置输出电压。"}, {"pin_number": "8", "pin_name": "EN", "pin_description": "使能引脚。使能引脚打开或关闭 DC/DC 开关转换器。启动前浮动 EN 引脚会禁用转换器。EN 引脚的推荐工作条件为最大 5.5V。不要将 EN 引脚直接连接到 VIN 引脚。"}, {"pin_number": "9", "pin_name": "PGOOD", "pin_description": "开漏电源良好状态信号。当 FB 电压移出指定限制时，PGOOD 在 2µs 延迟后变为低电平。"}, {"pin_number": "10, 21", "pin_name": "VIN", "pin_description": "集成电源 MOSFET 对和内部 LDO 的电源输入引脚。将去耦输入电容器从 VIN 引脚尽可能靠近地放置到 PGND 引脚。"}, {"pin_number": "11, 12, 13, 14, 15, 16, 17, 18", "pin_name": "PGND", "pin_description": "内部低侧 MOSFET 的电源地。至少需要六个 PGND 过孔尽可能靠近 PGND 引脚放置。这可以最小化寄生阻抗并降低热阻。"}, {"pin_number": "19", "pin_name": "VCC", "pin_description": "内部 3V LDO 输出。可以将具有 3.3V 或更高电压的外部偏置连接到此引脚，以节省内部 LDO 上的功率损耗。此引脚上的电压源为内部电路和栅极驱动器供电。需要一个 2.2µF、至少 6.3V 额定值的陶瓷电容器作为去耦电容器，从 VCC 引脚到 PGND 引脚，并且放置要求尽可能靠近。"}, {"pin_number": "20", "pin_name": "SW", "pin_description": "电源转换器的输出开关端。将此引脚连接到输出电感器。"}]}], "datasheet_cn": {"name": "TPS548B28", "path": "用户上传的PDF", "release_date": "2022-12", "version": "ZHCSMV4A"}, "datasheet_en": {"name": "SNVSBC5", "path": "未找到", "release_date": "未找到", "version": "未找到"}, "family_comparison": "引脚与 15A TPS548A28 兼容", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "16", "min_input_voltage": "2.7", "max_output_voltage": "5.5", "min_output_voltage": "0.6", "max_output_current": "20", "max_switch_frequency": "1", "quiescent_current": "910", "high_side_mosfet_resistance": "7.7", "low_side_mosfet_resistance": "2.4", "over_current_protection_threshold": "22.9", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "不适用", "enable_function": "Yes", "light_load_mode": "Eco-mode (自动跳跃)", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "1", "output_reference_voltage": "0.6", "loop_control_mode": "D-CAP3", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "16V", "min_input_voltage": "2.7V", "max_output_voltage": "5.5V", "min_output_voltage": "0.6V", "max_output_current": "20A", "max_switch_frequency": "1MHz", "quiescent_current": "910µA", "high_side_mosfet_resistance": "7.7mΩ", "low_side_mosfet_resistance": "2.4mΩ", "over_current_protection_threshold": "6-25A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Eco-mode (自动跳跃), FCCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "Yes", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.6V", "loop_control_mode": "D-CAP3"}}