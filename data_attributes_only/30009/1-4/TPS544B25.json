{"part_number": "TPS544C25", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "同步降压芯片", "part_number_title": "TPS544x25 4.5V 至 18V、20A 和 30A SWIFT TM 同步降压控制器 (具有 PMBus™ 和频率同步功能)", "features": ["符合 PMBus 1.2 标准的转换器: 20A 和 30A", "输入电压范围: 4.5V 至 18V", "输出电压范围: 0.5V 至 5.5V", "5mm x 7mm LQFN 封装", "单个散热焊盘", "集成 5.5mΩ 和 2.0mΩ 堆叠 NexFETTM 功率级", "用于通过 PMBus 进行自适应电压调节 (AVS) 和裕量调节的 500mV 至 1500mV 基准", "电压不低于 600mV 时的精度为 0.5%", "无损低侧金属氧化物半导体场效应晶体管 (MOSFET) 电流感测", "具有输入前馈功能的电压模式控制", "差分远程感应", "单启动至预偏置输出", "输出电压和输出电流报告", "使用 2N3904 时的外部温度监视晶体管", "可通过 PMBus 接口进行编程", "热关断", "引脚配置适用的开关频率: 200kHz 至 1MHz", "与外部时钟频率同步", "封装兼容 20A、30A 转换器"], "description": "TPS544x25 器件是采用 5mm × 7mm 封装且符合 PMBus 1.2 规范的非隔离式直流/直流转换器，具有集成的 FET，能够在高频下运行并输出 20A 或 30A 的电流。集成式 NexFET™ 功率级和经优化的驱动器提供的高频、低损耗开关支持密度极高的电源解决方案。PMBus 接口通过 VOUT_COMMAND 支持 AVS 功能，同时支持灵活的转换器配置以及关键参数（包括输出电压、电流和可选的外部温度）监控功能。对故障状况的响应可设置为重新启动、锁存或忽略，具体取决于系统要求。", "applications": ["测试和测量仪器", "以太网交换机、光交换机、路由器、基站", "服务器", "企业级存储固态硬盘 (SSD)", "高密度电源解决方案"], "ordering_information": [{"part_number": "TPS544C25", "order_device": "TPS544C25RVFR", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": "2500", "package_drawing_code": "RVF0040A", "marking": "TPS544C25", "pin_count": "40", "length": "7.00", "width": "5.00", "height": "1.52", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS544C25", "order_device": "TPS544C25RVFR.B", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": "2500", "package_drawing_code": "RVF0040A", "marking": "TPS544C25", "pin_count": "40", "length": "7.00", "width": "5.00", "height": "1.52", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS544C25", "order_device": "TPS544C25RVFRG4.B", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "LARGE T&R", "carrier_quantity": "2500", "package_drawing_code": "RVF0040A", "marking": "TPS544C25", "pin_count": "40", "length": "7.00", "width": "5.00", "height": "1.52", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS544C25", "order_device": "TPS544C25RVFT", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RVF0040A", "marking": "TPS544C25", "pin_count": "40", "length": "7.00", "width": "5.00", "height": "1.52", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS544C25", "order_device": "TPS544C25RVFT.B", "status": "Active", "package_type": "LQFN-CLIP", "package_code": "RVF", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RVF0040A", "marking": "TPS544C25", "pin_count": "40", "length": "7.00", "width": "5.00", "height": "1.52", "pitch": "未找到", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "未找到"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS544C25", "package_type": "LQFN-40", "pins": [{"pin_number": "1", "pin_name": "CNTL", "pin_description": "PMBus CNTL pin. See Supported PMBus Commands section. The CNTL pin has an internal pull-up and floats high when left floating."}, {"pin_number": "2", "pin_name": "ADDR1", "pin_description": "Sets high-order 3-bits of the PMBus address. Connect a resistor between this pin and AGND."}, {"pin_number": "3", "pin_name": "ADDR0", "pin_description": "Sets low-order 3-bits of the PMBus address. Connect a resistor between this pin and AGND."}, {"pin_number": "4", "pin_name": "DATA", "pin_description": "PMBus DATA pin. See Supported PMBus Commands section."}, {"pin_number": "5", "pin_name": "CLK", "pin_description": "PMBus CLK pin. See Supported PMBus Commands section."}, {"pin_number": "6", "pin_name": "SMBALERT", "pin_description": "SMBus alert pin. See SMBus specification."}, {"pin_number": "7", "pin_name": "BOOT", "pin_description": "Bootstrap pin for the internal flying high-side driver. Connect a typical 100-nF capacitor from this pin to the SW pin."}, {"pin_number": "8, 9, 10, 11, 12", "pin_name": "SW", "pin_description": "Switched power output of the device. Connect the output averaging filter and bootstrap capacitor to this group of pins."}, {"pin_number": "13, 14, 15, 16, 17, 18, 19, 20", "pin_name": "GND", "pin_description": "Power stage ground return."}, {"pin_number": "21, 22, 23, 24, 25", "pin_name": "VIN", "pin_description": "Input power to the power stage. Low impedance bypassing of these pins to GND is critical."}, {"pin_number": "26", "pin_name": "PGND", "pin_description": "Power ground return for controller device. Connect to GND at the thermal tab."}, {"pin_number": "27", "pin_name": "BP3", "pin_description": "Output of the 3.3-V on-board regulator. This regulator powers the controller and should be bypassed with a minimum of 2.2 µF to AGND. BP3 is not designed to power external circuit."}, {"pin_number": "28", "pin_name": "BP6", "pin_description": "Output of the 6.5-V on-board regulator. This regulator powers the driver stage of the controller and should be bypassed with a minimum of 2.2 µF to GND. TI recommends using an additional 100-nF typical bypass capacitor for reduce ripple on BP6."}, {"pin_number": "29", "pin_name": "VDD", "pin_description": "Input power to the controller. Connect a low impedance bypass with a minimum of 1 µF to AGND. The VDD voltage is also used for input feed-forward. VIN and VDD must be the same potential for accurate short circuit protection."}, {"pin_number": "30", "pin_name": "VSET", "pin_description": "Optionally configures default output voltage setting by connecting a resistor from this pin to AGND. See Set Default Output Voltage by VSET for details. If VSET is not used, pull this pin up to BP3. Do not leave this pin floating."}, {"pin_number": "31", "pin_name": "VOUTS+", "pin_description": "Load voltage sensing, positive side. This sensing provides remote sensing for the PMBus interface reporting and the voltage control loop."}, {"pin_number": "32", "pin_name": "VOUTS-", "pin_description": "Load voltage sensing, negative or common side. This sensing provides remote sensing for the PMBus interface reporting and the voltage control loop."}, {"pin_number": "33", "pin_name": "DIFFO", "pin_description": "Output of the differential remote sense amplifier."}, {"pin_number": "34", "pin_name": "FB", "pin_description": "Feedback pin for the control loop. Negative input of the error amplifier."}, {"pin_number": "35", "pin_name": "COMP", "pin_description": "Output of the error amplifier. Connect compensator network from this pin to the FB pin."}, {"pin_number": "36", "pin_name": "PGOOD", "pin_description": "Power good output. Open drain output that floats up when the device is operating and in regulation. Any fault condition causes this pin to pull low."}, {"pin_number": "37", "pin_name": "TSNS/SS", "pin_description": "External temperature sense signal input or alternatively used to set default soft-start time by connecting a resistor from this pin to AGND. Do not leave this pin floating."}, {"pin_number": "38", "pin_name": "AGND", "pin_description": "Analog ground return for controller device. Connect to GND at the thermal tab."}, {"pin_number": "39", "pin_name": "SYNC/RESET_B", "pin_description": "For switching frequency synchronization or output voltage reset."}, {"pin_number": "40", "pin_name": "RT", "pin_description": "Frequency-setting resistor. Connect a resistor from this pin to AGND to program the switching frequency. Do not leave this pin floating."}, {"pin_number": "Thermal Tab", "pin_name": "Thermal Tab", "pin_description": "Package thermal tab. Connect to GND. The thermal tab must have adequate solder coverage for proper operation."}]}], "datasheet_cn": {"datasheet_name": "ZHCSDR8–MAY 2015", "datasheet_path": "未找到", "release_date": "2015-05", "version": "ZHCSDR8"}, "datasheet_en": {"datasheet_name": "SLUSC81", "datasheet_path": "未找到", "release_date": "未找到", "version": "未找到"}, "family_comparison": "TPS544C25: 30A, TPS544B25: 20A", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "18", "min_input_voltage": "4.5", "max_output_voltage": "5.5", "min_output_voltage": "0.5", "max_output_current": "30", "max_switch_frequency": "1", "quiescent_current": "9.5", "high_side_mosfet_resistance": "5.5", "low_side_mosfet_resistance": "2", "over_current_protection_threshold": "36", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "No", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Hiccup", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "0.5", "output_reference_voltage": "0.95", "loop_control_mode": "电压模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "4.5V", "max_output_voltage": "5.5V", "min_output_voltage": "0.5V", "max_output_current": "30A", "max_switch_frequency": "1MHz", "quiescent_current": "9500µA", "high_side_mosfet_resistance": "5.5mΩ", "low_side_mosfet_resistance": "2.0mΩ", "over_current_protection_threshold": "36A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "PMBus", "enable_function": "Yes", "light_load_mode": "CCM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "间歇式", "output_under_voltage_protection": "间歇式", "output_over_load_protection": "间歇式", "output_short_circuit_protection": "间歇式", "over_temperature_protection": "自动重启", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "Yes", "output_voltage_tracking": "No", "dynamic_voltage_setting": "Yes", "output_voltage_accuracy": "±0.5%", "output_reference_voltage": "0.95V", "loop_control_mode": "电压模式"}}