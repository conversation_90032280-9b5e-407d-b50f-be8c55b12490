{"part_number": "RAA212831", "manufacturer": "Renesas", "country": "Japan", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片", "part_number_title": "4.5V to 72V Input, 0.5A Buck Regulator with Two Linear Regulator Outputs", "features": ["<PERSON> Converter:", "4.5V to 72V input voltage", "Adjustable output voltage from 1.25V to VIN×Dmax", "500mA output load capability", "0.6Ω high-side MOSEFT rDS(ON)", "Fixed switching frequency 350kHz in CCM operation", "Pulse skipping mode in DCM operation", "6V standby function by external components", "High-side OCP, UVP, UVLO, OTP fault protection", "5V LDO Regulator:", "6V to 12V input voltage", "Fixed output voltage 5V", "100mA output load capability", "Current limit foldback function", "3.3V LDO Regulator:", "4.5V to 12V input voltage", "Fixed output voltage 3.3V", "50mA output load capability", "Current limit foldback function", "SOIC8-E package"], "description": "The RAA212831 is a triple output regulator combining a 4.5V to 72V input, 0.5A buck regulator with two LDO outputs. The buck regulator has a fixed switching frequency of 350kHz in Continuous Conduction mode (CCM) and operates in pulse skipping mode at lighter loads when it enters Discontinuous Conduction mode (DCM). The two LDOs operate from a 12V (or lower) input voltage. The LDOs are rated at 100mA and 50mA of output current. The buck regulator output can be set from 1.25V to VIN×Dmax. The LDOs can support an input voltage range of 6V to 12V and the output voltages are fixed at 3.3V and 5V. The IC is designed to provide a compact, highly integrated power management solution. Its integrated buck regulator and LDO outputs minimize system component count.", "applications": ["Electric-bike power management", "Motor driver control board power supply"], "ordering_information": [{"part_number": "RAA212831", "order_device": "RAA2128314GSP#HA0", "status": "Active", "package_type": "EPSOIC", "package_code": "M8.15H", "carrier_description": "Tape & Reel", "carrier_quantity": 2500, "package_drawing_code": "M8.15H", "marking": "RAA 212831", "pin_count": 8, "length": 4.9, "width": 3.9, "height": 1.7, "pitch": 1.27, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "RAA212831", "package_type": "SOIC8-E", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "Input voltage for the IC and buck converter"}, {"pin_number": "2", "pin_name": "GND", "pin_description": "Ground"}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback pin. 1.25V ±3% over temperature range"}, {"pin_number": "4", "pin_name": "LDO_3V3", "pin_description": "LDO output voltage, fixed 3.3V"}, {"pin_number": "5", "pin_name": "LDO_5V", "pin_description": "LDO output voltage, fixed 5V"}, {"pin_number": "6", "pin_name": "LDO_VIN", "pin_description": "Input voltage for the LDOs"}, {"pin_number": "7", "pin_name": "BST", "pin_description": "Bootstrap supply pin"}, {"pin_number": "8", "pin_name": "SW", "pin_description": "Switch node output"}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "RAA212831 Datasheet", "path": "用户上传文件", "release_date": "2023-03-02", "version": "1.02"}, "family_comparison": {"RAA212831": {"Buck VOUT": "1.25V to VIN*Dmax by FB resistor settings", "Pin Difference": "FB Pin"}, "RAA212832": {"Buck VOUT": "6V or 12V by STBY pin settings", "Pin Difference": "STBY Pin"}}, "power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 72, "min_input_voltage": 4.5, "max_output_voltage": "VIN × Dmax", "min_output_voltage": 1.25, "max_output_current": 0.5, "max_switch_frequency": 0.389, "quiescent_current": 325, "high_side_mosfet_resistance": 600, "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": 0.82, "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 3, "output_reference_voltage": 1.25, "loop_control_mode": "Peak Current Mode", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "72V", "min_input_voltage": "4.5V", "max_output_voltage": "VIN × Dmax", "min_output_voltage": "1.25V", "max_output_current": "0.5A", "max_switch_frequency": "0.35MHz", "quiescent_current": "325µA", "high_side_mosfet_resistance": "600mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "0.82A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "No", "light_load_mode": "Pulse Skipping", "power_good_indicator": "No", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "3%", "output_reference_voltage": "1.25V", "loop_control_mode": "峰值电流模式"}}