{"part_number": "MP8764", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND (Not Recommended for New Designs)", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "High Efficiency, 12A, 18V Synchronous Step-Down Converter with Latch-off OCP", "features": ["2.5V to 18V Operating Input Range with External 5V Bias", "4.5V to 18V Operating Input Range with Internal Bias", "12A Output Current", "Low RDS(ON) Internal Power MOSFETs", "Proprietary Switching Loss Reduction Technique", "Adaptive COT for Ultrafast Transient Response", "1.5% Reference Voltage Over -40°C to +125°C Junction Temperature Range", "Programmable Soft Start Time", "Pre-Bias Start up", "Programmable Switching Frequency from 200kHz to 1MHz", "OVP, latch-off SCP, Protection and Thermal Shutdown", "Output Adjustable from 0.611V to 13V"], "description": "The MP8764 is a fully integrated high frequency synchronous rectified step-down switch mode converter. It offers a very compact solution to achieve 12A output current over a wide input supply range with excellent load and line regulation. The MP8764 operates at high efficiency over a wide output current load range. The MP8764 adopts Constant-On-Time (COT) control mode that provides fast transient response and eases loop stabilization. Operation frequency can be programmed easily from 200kHz to 1MHz by an external resistor and keeps nearly constant as input supply varies by the feedforward compensation. VCC under voltage lockout is internally set at 3.9V, but can be increased by programming the threshold with a resistor network on the enable pin. The output voltage startup ramp is controlled by the soft start pin. An open drain power good signal indicates the output is within its nominal voltage range. Full integrated protection features include OCP, OVP and thermal shutdown. The MP8764 requires a minimum number of readily available standard external components and are available in QFN 3X4 package.", "applications": ["Set-top Boxes", "XDSL Modem/DSLAM", "Small-cell Base Stations", "Personal Video Recorders", "Flat Panel Television and Monitors", "Distributed Power Systems"], "ordering_information": [{"part_number": "MP8764", "order_device": "MP8764GLE", "status": "NRFND (Not Recommended for New Designs)", "package_type": "QFN", "package_code": "16-Pin QFN (3x4mm)", "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO220", "marking": "MP8764 E", "pin_count": "16", "length": "4", "width": "3", "height": "未找到", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MP8764", "package_type": "16-<PERSON><PERSON>", "pins": [{"pin_number": "1", "pin_name": "EN", "pin_description": "Enable pin. EN is a digital input that turns the regulator on or off. Drive EN high to turn on the regulator, drive it low to turn it off. Connect EN to IN through a pull-up resistor or a resistive voltage divider for automatic startup. Do not float this pin. See Enable Control section for more details."}, {"pin_number": "2", "pin_name": "FREQ", "pin_description": "Frequency set during CCM operation. A resistor connected between FREQ and IN is required to set the switching frequency. The ON time is determined by the input voltage and the resistor connected to the FREQ pin. IN connect through a resistor is used for line feed-forward and makes the frequency basically constant during input voltage's variation."}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback. An external resistor divider from the output to GND, tapped to the FB pin, sets the output voltage. It is recommended to place the resistor divider as close to FB pin as possible. Vias should be avoided on the FB traces."}, {"pin_number": "4", "pin_name": "SS", "pin_description": "Soft Start. Connect an external capacitor to program the soft start time for the switch mode regulator."}, {"pin_number": "5", "pin_name": "AGND", "pin_description": "Analog ground. Select this pin as the control circuit reference point."}, {"pin_number": "6", "pin_name": "PG", "pin_description": "Power good output, the output of this pin is an open drain signal and a pull-up resistor connected to a DC voltage is required to indicate high if the output voltage is higher than 97% of the nominal voltage. There is a delay from FB ≥ 97% to PG goes high."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "Internal 4.8V LDO output. The driver and control circuits are powered from this voltage. Decouple with a minimum 1µF ceramic capacitor as close to the pin as possible. X7R or X5R grade dielectric ceramic capacitors are recommended for their stable temperature characteristics."}, {"pin_number": "8", "pin_name": "BST", "pin_description": "Bootstrap. A capacitor connected between SW and BST pins is required to form a floating supply across the high-side switch driver."}, {"pin_number": "9, 14", "pin_name": "IN", "pin_description": "Supply Voltage. The IN pin supplies power for internal MOSFET and regulator. The MP8764 operates from a +2.5V to +18V input rail with 5V external bias and a +4.5V to +18V input rail with internal bias. An input capacitor is needed to decouple the input rail. Use wide PCB traces and multiple vias to make the connection."}, {"pin_number": "10,11,12,13", "pin_name": "PGND", "pin_description": "System Ground. This pin is the reference ground of the regulated output voltage. For this reason care must be taken in PCB layout. Use wide PCB traces to make the connection."}, {"pin_number": "15, 16", "pin_name": "SW", "pin_description": "Switch Output. Connect this pin to the inductor and bootstrap capacitor. This pin is driven up to the VIN voltage by the high-side switch during the on-time of the PWM duty cycle. The inductor current drives the SW pin negative during the off-time. The on-resistance of the low-side switch and the internal Schottky diode fixes the negative voltage. Use wide PCB traces to make the connection."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "MP8764 Rev. 1.2", "path": "用户上传文件", "release_date": "2020-02-26", "version": "1.2"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "4.5V", "max_output_voltage": "13V", "min_output_voltage": "0.611V", "max_output_current": "12A", "max_switch_frequency": "1MHz", "quiescent_current": "860μA", "high_side_mosfet_resistance": "21mΩ", "low_side_mosfet_resistance": "7mΩ", "over_current_protection_threshold": "12A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Skip Mode", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1.5%", "output_reference_voltage": "0.611V", "loop_control_mode": "固定导通时间控制"}, "extraction_metadata": {"extractor": "prompt_pdf_optimized", "extraction_date": "2024-05-23T10:15:00Z", "pdf_info": {"file_name": "MP8764.pdf", "page_count": 23}}}