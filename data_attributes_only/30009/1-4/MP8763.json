{"part_number": "MP8763", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND (Not Recommended for New Designs)", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC变换器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "High Efficiency, 12A, 18V, Synchronous, Step-Down Converter", "features": ["2.5V to 18V Operating Input Range with External 5V Bias", "4.5V to 18V Operating Input Range with Internal Bias", "12A Output Current", "Low RDS(ON) Internal Power MOSFETs", "Proprietary Switching-Loss-Reduction Technique", "Adaptive COT for Ultrafast Transient Response", "1% Reference Voltage Over Junction Temperature Range (0°C to +125°C)", "Programmable Soft-Start Time", "Pre-Bias Start-Up", "Programmable Switching Frequency from 200kHz to 1MHz", "Non-Latch OCP, OVP, and Thermal Shutdown", "Output Adjustable from 0.611V to 13V"], "description": "The MP8763 is a fully-integrated, high-frequency, synchronous, rectified, step-down, switch-mode converter. It offers a very compact solution to achieve a 12A output current over a wide input-supply range with excellent load and line regulation. The MP8763 operates at high efficiency over a wide output-current load range. The MP8763 uses Constant-On-Time (COT) control mode to provide fast transient response and ease loop stabilization. An external resistor programs the operating frequency from 200kHz to 1MHz. The frequency stays nearly constant as the input supply varies with the feed-forward compensation. The default under voltage lockout threshold is internally set at less than 4.1V, but a resistor network on the enable pin can increase this threshold. The soft start pin controls the output voltage startup ramp. An open drain power good signal indicates that the output is within nominal voltage range. It has full integrated protection features that include over-current protection, over-voltage protection and thermal shutdown. The MP8763 is available in a 3mm×4mm QFN package, and requires a minimal number of readily-available components.", "applications": ["Set-Top Boxes", "XDSL Modem/DSLAM", "Small-Cell Base Stations", "Personal Video Recorders", "Flat-Panel Televisions and Monitors", "Distributed Power Systems"], "ordering_information": [{"part_number": "MP8763", "order_device": "MP8763GL", "status": "Not recommended for new design", "package_type": "QFN", "package_code": "QFN-13", "carrier_description": "Tape & Reel", "package_drawing_code": "MO-220", "marking": "MP8763", "pin_count": 13, "length": 4, "width": 3, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 125}, {"part_number": "MP8763", "order_device": "MP8763GLE", "status": "Recommended for new designs", "package_type": "QFN", "package_code": "QFN-16", "carrier_description": "Tape & Reel", "package_drawing_code": "MO220", "marking": "MP8763E", "pin_count": 16, "length": 4, "width": 3, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 125}], "typical_application_circuit": "有", "pin_config": "有", "function_block_diagram": "有", "pin_function": [{"product_part_number": "MP8763", "package_type": "QFN-13", "pins": [{"pin_number": "1", "pin_name": "EN", "pin_description": "Enable. Digital input to turn the regulator ON or OFF. Drive EN HIGH to turn on the regulator. Drive it LOW to turn it off. Connect EN to IN through a pull-up resistor or a resistive voltage divider for automatic startup. Do not float this pin."}, {"pin_number": "2", "pin_name": "FREQ", "pin_description": "Frequency Set. Requires a resistor connected between FREQ and IN to set the switching frequency. The input voltage and the resistor connected to the FREQ pin determine the ON time. The connection to the IN pin provides line feed-forward and stabilizes the frequency during input voltage variation."}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback. Connect to the tab of an external resistor divider from the output to GND to set the output voltage. FB is also configured to realize over-voltage protection (OVP) by monitoring output voltage. Place the resistor divider as close to FB pin as possible. Avoid using vias on the FB traces."}, {"pin_number": "4", "pin_name": "SS", "pin_description": "Soft-Start. Connect an external capacitor to program the soft start time for the switch mode regulator."}, {"pin_number": "5", "pin_name": "AGND", "pin_description": "Analog ground. The control circuit reference."}, {"pin_number": "6", "pin_name": "PG", "pin_description": "Power Good, the output is an open drain signal. Require a pull-up resistor to a DC voltage to indicate high if the output voltage exceeds 91% of the nominal voltage. There is a delay from FB ≥ 91% to PG goes high."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "Internal 4.8V LDO output. Power the driver and control circuits. 5V external bias can disable the internal LDO. Decoupling with ≥1µF ceramic capacitor as close to the pin as possible. Use X7R or X5R dielectric ceramic capacitors for their stable temperature characteristics."}, {"pin_number": "8", "pin_name": "BST", "pin_description": "Bootstrap. Require a capacitor connected between SW and BST pins to form a floating supply across the high-side switch driver."}, {"pin_number": "9, 10", "pin_name": "SW", "pin_description": "Switch Output. Connect to the inductor and bootstrap capacitor. The high-side switch drives the pin up to the VIN voltage during PWM duty cycle's ON time. The inductor current drives the SW pin negative during the OFF-time. The low-side switch's ON-resistance and the internal Schottky diode clamp the negative voltage. Connect using wide PCB traces."}, {"pin_number": "11, 12", "pin_name": "PGND", "pin_description": "System Ground. Reference ground of the regulated output voltage. PCB layout requires extra care. Connect using wide PCB traces."}, {"pin_number": "13", "pin_name": "IN", "pin_description": "Supply Voltage. Supplies power to the internal MOSFET and regulator. The MP8763 operates from a +2.5V to +18V input rail with 5V external bias and a +4.5V to +18V input rail with internal bias. Requires an input decoupling capacitor. Connect using wide PCB traces and multiple vias."}]}, {"product_part_number": "MP8763", "package_type": "QFN-16", "pins": [{"pin_number": "1", "pin_name": "EN", "pin_description": "Enable. Digital input to turn the regulator ON or OFF. Drive EN HIGH to turn on the regulator. Drive it LOW to turn it off. Connect EN to IN through a pull-up resistor or a resistive voltage divider for automatic startup. Do not float this pin."}, {"pin_number": "2", "pin_name": "FREQ", "pin_description": "Frequency Set. Requires a resistor connected between FREQ and IN to set the switching frequency. The input voltage and the resistor connected to the FREQ pin determine the ON time. The connection to the IN pin provides line feed-forward and stabilizes the frequency during input voltage variation."}, {"pin_number": "3", "pin_name": "FB", "pin_description": "Feedback. Connect to the tab of an external resistor divider from the output to GND to set the output voltage. FB is also configured to realize over-voltage protection (OVP) by monitoring output voltage. Place the resistor divider as close to FB pin as possible. Avoid using vias on the FB traces."}, {"pin_number": "4", "pin_name": "SS", "pin_description": "Soft-Start. Connect an external capacitor to program the soft start time for the switch mode regulator."}, {"pin_number": "5", "pin_name": "AGND", "pin_description": "Analog ground. The control circuit reference."}, {"pin_number": "6", "pin_name": "PG", "pin_description": "Power Good, the output is an open drain signal. Require a pull-up resistor to a DC voltage to indicate high if the output voltage exceeds 91% of the nominal voltage. There is a delay from FB ≥ 91% to PG goes high."}, {"pin_number": "7", "pin_name": "VCC", "pin_description": "Internal 4.8V LDO output. Power the driver and control circuits. 5V external bias can disable the internal LDO. Decoupling with ≥1µF ceramic capacitor as close to the pin as possible. Use X7R or X5R dielectric ceramic capacitors for their stable temperature characteristics."}, {"pin_number": "8", "pin_name": "BST", "pin_description": "Bootstrap. Require a capacitor connected between SW and BST pins to form a floating supply across the high-side switch driver."}, {"pin_number": "10,11,12,13", "pin_name": "PGND", "pin_description": "System Ground. Reference ground of the regulated output voltage. PCB layout requires extra care. Connect using wide PCB traces."}, {"pin_number": "9, 14", "pin_name": "IN", "pin_description": "Supply Voltage. Supplies power to the internal MOSFET and regulator. The MP8763 operates from a +2.5V to +18V input rail with 5V external bias and a +4.5V to +18V input rail with internal bias. Requires an input decoupling capacitor. Connect using wide PCB traces and multiple vias."}, {"pin_number": "15, 16", "pin_name": "SW", "pin_description": "Switch Output. Connect to the inductor and bootstrap capacitor. The high-side switch drives the pin up to the VIN voltage during PWM duty cycle's ON time. The inductor current drives the SW pin negative during the OFF-time. The low-side switch's ON-resistance and the internal Schottky diode clamp the negative voltage. Connect using wide PCB traces."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MP8763", "datasheet_path": "用户上传", "release_date": "2020-02-26", "version": "1.3"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 18, "min_input_voltage": 4.5, "max_output_voltage": 13, "min_output_voltage": 0.611, "max_output_current": 12, "max_switch_frequency": 1, "quiescent_current": 860, "high_side_mosfet_resistance": 19.6, "low_side_mosfet_resistance": 5.2, "over_current_protection_threshold": 15, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "未找到", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "未找到", "output_discharge": "未找到", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1, "output_reference_voltage": 0.611, "loop_control_mode": "固定导通时间控制", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "18V", "min_input_voltage": "4.5V", "max_output_voltage": "13V", "min_output_voltage": "0.611V", "max_output_current": "12A", "max_switch_frequency": "1MHz", "quiescent_current": "860μA", "high_side_mosfet_resistance": "19.6mΩ", "low_side_mosfet_resistance": "5.2mΩ", "over_current_protection_threshold": "15A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1%", "output_reference_voltage": "0.611V", "loop_control_mode": "固定导通时间控制"}}