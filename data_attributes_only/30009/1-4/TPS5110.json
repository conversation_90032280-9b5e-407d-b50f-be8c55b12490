{"part_number": "TPS5110", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关控制器", "category_lv3": "降压控制器", "part_number_title": "SYNCHRONOUS-<PERSON><PERSON><PERSON> PWM CONTROLLER WITH NMOS LDO CONTROLLER", "features": ["Switching Mode Step-Down dc-to-dc Controller With Fast LDO Controller", "Input Voltage Range: Switcher: 4.5 V to 28 V, LDO: 1.1 V to 3.6 V", "Output Voltage Range: Switcher: 0.9 V to 3.5 V, LDO: 0.9 V to 2.5 V", "Synchronous for High Efficiency", "Precision VREF (±1 %)", "PWM Mode Control: Max. 500-kHz Operation", "High-Speed Error Amplifier", "Overcurrent Protection With Temperature Compensation Circuit", "Overvoltage and Undervoltage Protection", "Programmable Short-Circuit Protection"], "description": "The TPS5110 provides one PWM-mode synchronous buck regulator controller (SBRC) and one low drop-out (LDO) regulator controller. The TPS5110 supports a low-voltage/high-current power supply for I/O and other peripherals in modern digital systems. The SBRC of the TPS5110 automatically adjusts from PWM mode to SKIP mode to maintain high efficiency under all load conditions. The LDO controller drives an external N-channel power MOSFET that realizes fast response and ultra-low dropout voltage. A unique overshoot protection circuit prevents a voltage hump at fast load decreasing transients. The current protection circuit for SBRC detects the drain-to-source voltage drop across the low-side and high-side power MOSFET while it is conducting. Also, the current protection circuit has a temperature coefficient to compensate for the RDS(on) variation of the MOSFET. This resistor-less current protection simplifies the system design and reduces the external parts count. The LDO controller includes current-limit protection. Other features, such as undervoltage lockout, power good, overvoltage, undervoltage, and programmable short-circuit protection promote system reliability.", "applications": ["Notebook PCs, PDAs", "Consumer Game Systems", "DSP Application"], "ordering_information": [{"part_number": "TPS5110", "order_device": "TPS5110PW", "status": "Active", "package_type": "TSSOP", "package_code": "PW", "carrier_description": "TUBE", "carrier_quantity": 60, "package_drawing_code": "PW0024A", "marking": "PS5110", "pin_count": 24, "length": 7.9, "width": 4.5, "height": 1.2, "pitch": 0.65, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "Industrial"}, {"part_number": "TPS5110", "order_device": "TPS5110PW.B", "status": "Active", "package_type": "TSSOP", "package_code": "PW", "carrier_description": "TUBE", "carrier_quantity": 60, "package_drawing_code": "PW0024A", "marking": "PS5110", "pin_count": 24, "length": 7.9, "width": 4.5, "height": 1.2, "pitch": 0.65, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "Industrial"}, {"part_number": "TPS5110", "order_device": "TPS5110PWR", "status": "Active", "package_type": "TSSOP", "package_code": "PW", "carrier_description": "LARGE T&R", "carrier_quantity": 2000, "package_drawing_code": "PW0024A", "marking": "PS5110", "pin_count": 24, "length": 7.9, "width": 4.5, "height": 1.2, "pitch": 0.65, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "Industrial"}, {"part_number": "TPS5110", "order_device": "TPS5110PWR.B", "status": "Active", "package_type": "TSSOP", "package_code": "PW", "carrier_description": "LARGE T&R", "carrier_quantity": 2000, "package_drawing_code": "PW0024A", "marking": "PS5110", "pin_count": 24, "length": 7.9, "width": 4.5, "height": 1.2, "pitch": 0.65, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "Industrial"}], "typical_application_circuit": "存在", "pin_config": "存在", "function_block_diagram": "存在", "pin_function": [{"product_part_number": "TPS5110", "package_type": "TSSOP", "pins": [{"pin_number": "1", "pin_name": "INV", "pin_description": "Inverting input of the SBRC error amplifier, skip comparator, OVP/UVP comparators and POWERGOOD comparator"}, {"pin_number": "2", "pin_name": "FB", "pin_description": "Feedback output of error amplifier"}, {"pin_number": "3", "pin_name": "SOFTSTART", "pin_description": "External capacitor between SOFTSTART and GND sets SBRC soft-start time."}, {"pin_number": "4", "pin_name": "PWM_SEL", "pin_description": "PWM or auto PWM/SKIP modes select. H: auto PWM/SKIP, L: PWM fixed"}, {"pin_number": "5", "pin_name": "CT", "pin_description": "External capacitor from CT to GND adjusts frequency of the triangle oscillator."}, {"pin_number": "6", "pin_name": "GND", "pin_description": "Signal GND"}, {"pin_number": "7", "pin_name": "REF", "pin_description": "0.85-V reference voltage output. This 0.85-V reference voltage is used for setting the output voltage and the voltage protections. This reference voltage is regulated from REG5V_IN power supply."}, {"pin_number": "8", "pin_name": "STBY", "pin_description": "Standby control input for SBRC. SBRC can be switched into standby mode by grounding the STBY pin."}, {"pin_number": "9", "pin_name": "STBY_LDO", "pin_description": "Standby control input for LDO regulator. LDO regulator can be switched into standby mode by grounding the STBY_LDO pin."}, {"pin_number": "10", "pin_name": "FLT", "pin_description": "Fault latch timer pin. An external capacitor is connected between FLT and GND to set the FLT enable time up."}, {"pin_number": "11", "pin_name": "POWERGOOD", "pin_description": "Power good open-drain output. PG comparators monitor both SBRC's and LDO's over voltage and under voltage. The threshold is ±7%. When either output is beyond this condition, POWERGOOD output goes low. When STBY or STBY_LDO goes high, the POWERGOOD pin's output starts with high. POWERGOOD also monitors REG5V_IN's UVLO output."}, {"pin_number": "12", "pin_name": "INV_LDO", "pin_description": "Inverting input of the LDO regulator, OVP/UVP comparators and POWERGOOD comparator"}, {"pin_number": "13", "pin_name": "LDO_OUT", "pin_description": "LDO regulator's output connection. If output voltage causes an over shoot at output current changes high to low quickly, it pulls out electrical charge from this pin."}, {"pin_number": "14", "pin_name": "LDO_GATE", "pin_description": "Gate control output of an external MOSFET for LDO"}, {"pin_number": "15", "pin_name": "LDO_CUR", "pin_description": "Current sense input of the LDO regulator."}, {"pin_number": "16", "pin_name": "LDO_IN", "pin_description": "Input of LDO regulator and current sense input of LDO regulator"}, {"pin_number": "17", "pin_name": "REG5V_IN", "pin_description": "External 5-V input. This input is a supply voltage for internal circuits."}, {"pin_number": "18", "pin_name": "VIN_SENSE", "pin_description": "SBRC supply voltage monitor. Input range is 4.5 V to 28 V. This pin is for reference of current limit."}, {"pin_number": "19", "pin_name": "TRIP", "pin_description": "External resistor connection for SBRC's output current protection control."}, {"pin_number": "20", "pin_name": "OUTGND", "pin_description": "Ground for FET drivers. It is connected to the current limiting comparator's negative input."}, {"pin_number": "21", "pin_name": "OUT_d", "pin_description": "Gate drive output for low-side MOSFET(s)"}, {"pin_number": "22", "pin_name": "LL", "pin_description": "High side gate driving return. Connect this pin to the junction of the high side and low side MOSFET(s) for floating drive configuration. This pin also is an input terminal for current comparator."}, {"pin_number": "23", "pin_name": "OUT_u", "pin_description": "Gate drive output for high-side MOSFET(s)."}, {"pin_number": "24", "pin_name": "LH", "pin_description": "Bootstrap capacitor connection for high-side gate driver"}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "TPS5110", "datasheet_path": "本地", "release_date": "2004-07-01", "version": "SLVS025B"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "28V", "min_input_voltage": "4.5V", "max_output_voltage": "3.5V", "min_output_voltage": "0.9V", "max_output_current": "6A", "max_switch_frequency": "0.5MHz", "quiescent_current": "900µA", "high_side_mosfet_resistance": "不适用(外置)", "low_side_mosfet_resistance": "不适用(外置)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "外部电阻分压", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM/PSM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "No", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.85V", "loop_control_mode": "电压模式"}}