{"part_number": "MP2305S", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "End of Life", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "2A, 23V Synchronous Rectified Step-Down Converter", "features": ["2A Output Current", "Wide 4.75V to 23V Operating Input Range", "Integrated Power MOSFET Switches", "Output Adjustable from 0.923V to 20V", "Up to 95% Efficiency", "Programmable Soft-Start", "Stable with Low ESR Ceramic Output Capacitors", "Fixed 340KHz Frequency", "Cycle-by-Cycle Over Current Protection", "Input Under Voltage Lockout"], "description": "The MP2305S is a monolithic synchronous buck regulator. The device integrates a 175mΩ high-side MOSFET and a 115mΩ low-side MOSFET that provide 2A continuous load current over a wide operating input voltage of 4.75V to 23V. Current mode control provides fast transient response and cycle-by-cycle current limit. An adjustable soft-start prevents inrush current at turn-on. Shutdown mode drops the supply current to 1μA. This device, available in an 8-pin SOIC package, provides a very compact system solution with minimal reliance on external components.", "applications": ["Distributed Power Systems", "Networking Systems", "FPGA, DSP, ASIC Power Supplies", "Green Electronics/Appliances", "Notebook Computers"], "ordering_information": [{"part_number": "MP2305S", "order_device": "MP2305SDS", "status": "End of Life", "package_type": "SOIC8", "package_code": "未找到", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "SOIC8", "marking": "M2305SDS", "pin_count": "8", "length": "4.9", "width": "3.9", "height": "1.55", "pitch": "1.27", "min_operation_temp": "-40", "max_operation_temp": "85", "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MP2305S", "package_type": "SOIC8", "pins": [{"pin_number": "1", "pin_name": "BS", "pin_description": "High-Side Gate Drive Boost Input. BS supplies the drive for the high-side N-Channel MOSFET switch. Connect a 0.01µF or greater capacitor from SW to BS to power the high side switch."}, {"pin_number": "2", "pin_name": "IN", "pin_description": "Power Input. IN supplies the power to the IC, as well as the step-down converter switches. Drive IN with a 4.75V to 23V power source. Bypass IN to GND with a suitably large capacitor to eliminate noise on the input to the IC. See Input Capacitor."}, {"pin_number": "3", "pin_name": "SW", "pin_description": "Power Switching Output. SW is the switching node that supplies power to the output. Connect the output LC filter from SW to the output load. Note that a capacitor is required from SW to BS to power the high-side switch."}, {"pin_number": "4", "pin_name": "GND", "pin_description": "Ground."}, {"pin_number": "5", "pin_name": "FB", "pin_description": "Feedback Input. FB senses the output voltage to regulate that voltage. Drive FB with a resistive voltage divider from the output voltage. The feedback threshold is 0.923V. See Setting the Output Voltage."}, {"pin_number": "6", "pin_name": "COMP", "pin_description": "Compensation Node. COMP is used to compensate the regulation control loop. Connect a series RC network from COMP to GND to compensate the regulation control loop. In some cases, an additional capacitor from COMP to GND is required. See Compensation Components."}, {"pin_number": "7", "pin_name": "EN", "pin_description": "Enable Input. EN is a digital input that turns the regulator on or off. Drive EN high to turn on the regulator, drive it low to turn it off. Pull up with 100kΩ resistor for automatic startup."}, {"pin_number": "8", "pin_name": "SS", "pin_description": "Soft-Start Control Input. SS controls the soft start period. Connect a capacitor from SS to GND to set the soft-start period. A 0.1µF capacitor sets the soft-start period to 15ms. To disable the soft-start feature, leave SS unconnected."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "MP2305S", "path": "用户上传的PDF", "release_date": "2023-01-18", "version": "1.02"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "23V", "min_input_voltage": "4.75V", "max_output_voltage": "20V", "min_output_voltage": "0.923V", "max_output_current": "2A", "max_switch_frequency": "0.34MHz", "quiescent_current": "1300µA", "high_side_mosfet_resistance": "175mΩ", "low_side_mosfet_resistance": "115mΩ", "over_current_protection_threshold": "4.1A", "operation_mode": "同步", "output_voltage_config_method": "外部可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2.5%", "output_reference_voltage": "0.923V", "loop_control_mode": "峰值电流模式"}}