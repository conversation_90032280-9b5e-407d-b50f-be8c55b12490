{"part_number": "TPS51388", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压芯片(<PERSON>)", "part_number_title": "TPS51388 4.5V 至 24V、12A 同步降压转换器", "features": ["输入电压范围为 4.5V 至 24V", "0.6V 至 5.5V 输出电压", "集成 9.2mΩ 和 4.5mΩ FET", "支持 12A 的连续输出电流", "90uA 低静态电流", "±1.0% 基准电压精度 (25°C)", "D-CAP3™ 控制模式架构控制，用于快速瞬态响应", "支持 POSCAP 和所有 MLCC 输出电容器", "可通过动态变化选择 Eco-mode 和 Out-of-Audio™ 模式", "ULQ™ 在系统待机期间延长电池寿命", "内置输出放电功能", "集成式电源正常状态指示器", "600kHz 和 1MHz 可选开关频率", "固定 1.0ms 软启动时间", "大占空比运行", "逐周期过流保护和负过流保护", "锁存输出 OV 和 UV 保护", "非锁存 UVLO 和 OT 保护", "-20°C 至 125°C 的工作结温范围", "13 引脚 3.0mm × 4.0mm HotRod™ VQFN 封装", "使用 TPS51388 并借助 WEBENCH® Power Designer 创建定制设计方案"], "description": "该器件是单片 12A 同步降压转换器，集成了 MOSFET，简单易用且高效，只需极少的外部元件，适合空间受限的电源系统。TPS51388 采用了 D-CAP3 控制模式，此控制模式只需内部补偿即可实现快速瞬态响应以及出色的线路和负载调整。ULQ 的延长电池寿命特性非常有益于在低功耗运行时延长电池寿命。输入电压较低时，大负荷运行可显著改善负载瞬态性能。可使用 EN 引脚来设置 Eco-Mode 或 Out-of-Audio (OOA) 模式，从而实现轻负载运行。Eco-mode 可在轻负载运行期间维持高效率。OOA 模式可将开关频率保持在可闻频率以上，同时将对效率的影响降至最低。即使转换器处于运行状态，也可以动态切换 EN 引脚。TPS51388 集成了电源正常状态指示器并具备输出放电功能。TPS51388 提供完整保护，包括 OVP、UVP、OCP、OTP 和 UVLO。此器件可采用 13 引脚 3.0mm × 4.0mm HotRod 封装，额定结温范围为 -20℃ 至 125°C。", "applications": ["笔记本电脑和台式机", "超极本、手持平板电脑", "工业 PC、单板计算机", "非军用无人机", "分布式电源系统"], "ordering_information": [{"part_number": "TPS51388", "order_device": "TPS51388VABR", "status": "Active", "package_type": "VQFN-HR", "package_code": "VAB", "carrier_description": "Tape & Reel", "carrier_quantity": 4000, "package_drawing_code": "VAB0013A", "marking": "51388", "pin_count": 13, "length": 4, "width": 3, "height": 1, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}, {"part_number": "TPS51388", "order_device": "TPS51388VABR.A", "status": "Active", "package_type": "VQFN-HR", "package_code": "VAB", "carrier_description": "Tape & Reel", "carrier_quantity": 4000, "package_drawing_code": "VAB0013A", "marking": "51388", "pin_count": 13, "length": 4, "width": 3, "height": 1, "min_operation_temp": -40, "max_operation_temp": 125, "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS51388", "package_type": "VQFN-HR", "pins": [{"pin_number": "1", "pin_name": "VIN", "pin_description": "控制电路的输入电压电源引脚。在 VIN 和 PGND 之间连接输入去耦电容器。"}, {"pin_number": "2,12", "pin_name": "SW", "pin_description": "连接到电感器和自举电容器以进行降压的开关节点。此引脚电压从低于接地电压的二极管电压摆动至降压的输入电压。"}, {"pin_number": "3,11", "pin_name": "PGND", "pin_description": "控制器电路和内部电路的电源 GND 端子。"}, {"pin_number": "4", "pin_name": "PG", "pin_description": "电源正常状态指示灯引脚。如果降压的输出电压由于热关断、压降、过压、EN 关断或缓慢启动而超出范围，则此引脚置为低电平。"}, {"pin_number": "5", "pin_name": "MODE", "pin_description": "模式选择引脚。将 MODE 引脚拉至大于 1V 的电压，将此引脚连接到 EN 或 VCC 可实现 600k 的工作频率。将 MODE 引脚拉低至 GND 可实现 1MHz 的工作频率。"}, {"pin_number": "6", "pin_name": "EN", "pin_description": "降压转换器的使能输入。EN 引脚还用于选择轻负载工作模式。将 EN 拉至 2.2V 以上可实现 Eco-mode。将 EN 拉至 1V 至 1.6V 之间可实现 OOA mode。"}, {"pin_number": "7", "pin_name": "FB", "pin_description": "转换器反馈输入，通过反馈电阻分压器将 FB 连接到输出电压。"}, {"pin_number": "8", "pin_name": "BYP", "pin_description": "外部 5V VCC 输入，将 1µF 去耦电容器靠近 BYP 和 PGND 放置，或者连接到降压稳压器的输出端 (如果输出电压设置为 4.8V 至 5.2V 之间)。该引脚还为内部 VCC LDO 提供旁路输入。BYP 外部电压必须在 VIN 电压导通后提供，并在 VIN 关断之前断开。"}, {"pin_number": "9", "pin_name": "FBG", "pin_description": "内部模拟电路的地。使用 4.7µF 陶瓷电容器将此引脚去耦合至地或将此引脚悬空。如果要提高负载瞬态性能，请连接到 Rfb_low。"}, {"pin_number": "10", "pin_name": "VCC", "pin_description": "内部 5V LDO 输出。内部模拟电路和驱动的电源。使用 2.2µF 陶瓷电容器将此引脚去耦合至地。"}, {"pin_number": "13", "pin_name": "VBST", "pin_description": "自举引脚。提供高侧栅极驱动器。在此引脚与 SW 引脚之间连接一个 0.1µF 陶瓷电容器。"}]}], "datasheet_cn": {"datasheet_name": "TPS51388", "datasheet_path": "TPS51388.pdf", "release_date": "SEPTEMBER 2024", "version": "ZHCSX13"}, "datasheet_en": {"datasheet_name": "TPS51388", "datasheet_path": "SLUSFU4", "release_date": "SEPTEMBER 2024", "version": "SLUSFU4"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 24, "min_input_voltage": 4.5, "max_output_voltage": 5.5, "min_output_voltage": 0.6, "max_output_current": 12, "max_switch_frequency": 1, "quiescent_current": 90, "high_side_mosfet_resistance": 9.2, "low_side_mosfet_resistance": 4.5, "over_current_protection_threshold": 14, "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "未找到", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1, "output_reference_voltage": 0.6, "loop_control_mode": "D-CAP3", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "24V", "min_input_voltage": "4.5V", "max_output_voltage": "5.5V", "min_output_voltage": "0.6V", "max_output_current": "12A", "max_switch_frequency": "1MHz", "quiescent_current": "90µA", "high_side_mosfet_resistance": "9.2mΩ", "low_side_mosfet_resistance": "4.5mΩ", "over_current_protection_threshold": "14A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Eco-mode, Out-of-Audio", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.6V", "loop_control_mode": "固定导通时间控制"}}