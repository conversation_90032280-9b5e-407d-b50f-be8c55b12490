{"part_number": "MPQ4572", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压型DC-DC", "part_number_title": "High-Efficiency, 2A, 60V, Fully Integrated, Synchronous Buck Converter, AEC-Q100 Qualified", "features": ["Wide 4.5V to 60V Operating Input Range", "2A Continuous Output Current", "High-Efficiency, Synchronous Mode Control", "250mΩ/45mΩ Internal Power MOSFETS", "Configurable Frequency Up to 2.2MHz", "180° Out-of-Phase SYNC Out Clock", "40μA Quiescent Current", "Low Shutdown Mode Current: 2μA", "FB-Tolerance: 1% at Room Temp, 2% at Full Temp", "Selectable AAM or Forced CCM Operation during Light-Load Conditions", "Internal 0.45ms Soft Start", "Remote EN Control", "Power Good Indicator", "Low-Dropout Mode", "Over-Current Protection (OCP)", "Short-Circuit Protection with Hiccup Mode", "VIN Under-Voltage Lockout (UVLO)", "Thermal Shutdown", "Available in a QFN-12 (2.5mmx3mm) Package", "Available in a Wettable Flank Package", "Available in AEC-Q100 Grade-1"], "description": "The MPQ4572 is a fully integrated, fixed-frequency, synchronous step-down converter. It can achieve up to 2A of continuous output current with peak current control for excellent transient response. The wide 4.5V to 60V input voltage range accommodates a variety of step-down applications in an automotive input environment. The device's 2μA shutdown mode quiescent current makes it ideal for battery-powered applications. The MPQ4572 integrates internal high-side and low-side power MOSFETs for high efficiency without an external <PERSON>hottky diode. It employs advanced asynchronous modulation (AAM) to achieve high efficiency during light-load conditions by scaling down the frequency to reduce switching and gate driver losses. Standard features include built-in soft start, enable control, and power good indication. High-duty cycle and low-dropout mode are provided for automotive cold crank conditions. The chip provides over-current protection (OCP) with valley-current detection to avoid current runaway. It also has hiccup short-circuit protection (SCP), input under-voltage lockout (UVLO), and auto-recovery thermal protection. With internal compensation, the MPQ4572 can offer a very compact solution with a minimal number of readily available, standard external components. It is available in a QFN-12 (2.5mmx3mm) package.", "applications": ["Automotive Infotainment", "Automotive Lamps and LEDs", "Automotive Motor Control", "Industrial Power Systems"], "ordering_information": [{"part_number": "MPQ4572", "order_device": "MPQ4572GQB-Z", "status": "Active", "package_type": "QFN-12", "package_code": "QFN-12 (2.5mmx3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": 5000, "package_drawing_code": "未找到", "marking": "AVN", "pin_count": 12, "length": 3.0, "width": 2.5, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 150, "output_voltage": "Adjustable", "application_grade": "Industrial"}, {"part_number": "MPQ4572", "order_device": "MPQ4572GQB-AEC1-Z", "status": "Active", "package_type": "QFN-12", "package_code": "QFN-12 (2.5mmx3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": 5000, "package_drawing_code": "未找到", "marking": "AVN", "pin_count": 12, "length": 3.0, "width": 2.5, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 150, "output_voltage": "Adjustable", "application_grade": "Automotive"}, {"part_number": "MPQ4572", "order_device": "MPQ4572GQBE-AEC1-Z", "status": "Active", "package_type": "QFN-12 Wettable Flank", "package_code": "QFN-12 (2.5mmx3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": 5000, "package_drawing_code": "未找到", "marking": "BMM", "pin_count": 12, "length": 3.0, "width": 2.5, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 150, "output_voltage": "Adjustable", "application_grade": "Automotive"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MPQ4572", "package_type": "QFN-12 (2.5mmx3mm)", "pins": [{"pin_number": "1", "pin_name": "BST", "pin_description": "Bootstrap. Connect a capacitor between SW and BST to form a floating supply across the high-side switch driver."}, {"pin_number": "2", "pin_name": "SW", "pin_description": "Switch output. SW is the output of the internal power switches. A wide PCB trace is recommended."}, {"pin_number": "3", "pin_name": "PG", "pin_description": "Power good indicator. The pin is an open drain; it requires a pull-up resistor to the power source. PG is pulled up to the power source if the output voltage is within 90% to 108% of the nominal voltage. It goes low when the output voltage exceeds 116% or falls below 84% of the nominal voltage."}, {"pin_number": "4", "pin_name": "FB", "pin_description": "Feedback point. FB is the negative input of the error amplifier. Connect FB to the tap of an external resistor divider between the output and GND to set the regulation voltage. In addition, power good and under-voltage lockout circuits use FB to monitor the output voltage."}, {"pin_number": "5", "pin_name": "FREQ", "pin_description": "Configurable switching frequency. Connect a resistor to GND to set the switching frequency."}, {"pin_number": "6", "pin_name": "VCC", "pin_description": "Internal bias supply. This pin supplies power to the internal control circuit and gate drivers. A decoupling capacitor (greater than 1μF) to ground is required close to this pin."}, {"pin_number": "7", "pin_name": "GND", "pin_description": "IC ground. Connect the pin to larger copper areas to the negative terminals of the input and output capacitors."}, {"pin_number": "8", "pin_name": "IN", "pin_description": "Input supply. This pin supplies all power to the converter. Place a decoupling capacitor to ground, as close as possible to the IC, to reduce switching spikes."}, {"pin_number": "9, 10", "pin_name": "NC", "pin_description": "No connection. These pins can be connected to GND to get better thermal and EMI performance in PCB layout."}, {"pin_number": "11", "pin_name": "CCM/SYNCO", "pin_description": "Mode selection/synchronization output. Connect the CCM pin to GND through a resistor (10kΩ to 300kΩ) to force the converter into CCM. Float this pin to force the converter into non-synchronous AAM mode under light-load conditions. CCM/SYNCO is also a synchronization output pin that can output a 180° out-of-phase clock to other devices."}, {"pin_number": "12", "pin_name": "EN", "pin_description": "Enable. Drive EN high to turn on the device; float or drive it low to turn off the device."}]}], "datasheet_cn": "未找到", "datasheet_en": {"name": "MPQ4572 Rev. 1.0", "path": "MPQ4572.pdf", "release_date": "2020-01-21", "version": "1.0"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "60V", "min_input_voltage": "4.5V", "max_output_voltage": "90% of VIN", "min_output_voltage": "1V", "max_output_current": "2A", "max_switch_frequency": "2.2MHz", "quiescent_current": "40µA", "high_side_mosfet_resistance": "250mΩ", "low_side_mosfet_resistance": "45mΩ", "over_current_protection_threshold": "3.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "AAM, FCCM", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "1", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}