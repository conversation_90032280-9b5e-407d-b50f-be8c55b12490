{"part_number": "TPS53015", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC芯片", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "具有 PGOOD 的 TPS53015 4.5V 至 28V 输入、D-CAP2TM 同步降压转换器", "features": ["D-CAP2TM模式控制", "快速瞬态响应", "无需外部组件即可实现环路补偿", "与陶瓷输出电容器兼容", "高初始基准精度 (±1%)", "宽输入电压范围: 4.5V 至 28V", "输出电压: 0.77V 至 7V", "低侧 RDS(on) 无损电流检测", "固定软启动时间: 1.4ms", "非灌电流预偏置软启动", "开关频率: 500kHz", "逐周期过流限制控制", "自动跳跃 Eco-Mode™确保在轻载时保持高效率", "电源正常状态输出", "OCL/OVP/UVP/UVLO/TSD 保护", "带有集成式升压 PMOS 开关的自适应栅极驱动器", "热补偿 OCP, 4000ppm/°C", "10 引脚 VSSOP"], "description": "TPS53015 器件是一款单通道、自适应导通时间 D-CAP2 TM模式同步降压控制器。该器件使系统设计人员能够以经济高效、低外部组件数和低待机电流的解决方案来完善包含各种终端设备电源总线稳压器的套件。TPS53015 的主控制环路采用D-CAP2 模式控制, 无需外部补偿组件即可实现极快的瞬态响应。自适应接通时间控制功能可支持较高负载条件下的PWM 模式与轻负载下的 Eco-mode™运行模式间的无缝转换。Eco-mode 运行模式使该器件能够在较轻负载条件下保持高效率。该器件也能适应低等效串联电阻 (ESR) 输出电容器（例如POSCAP 或 SP-CAP）和超低ESR 陶瓷电容器。该器件具有4.5V 至28V的输入电压和0.77V至7V的输出电压, 可实现便捷高效运行。TPS53015 采用 3mm × 3mm 10 引脚 VSSOP (DGS)封装, 额定环境温度范围为-40℃ 至 85℃。", "applications": ["用于多种应用的低功率系统负载点调节", "数字电视电源", "网络家庭终端设备", "数字机顶盒 (STB)", "DVD 播放器 / 录像机", "游戏机和其他"], "ordering_information": [{"part_number": "TPS53015", "order_device": "TPS53015DGS", "status": "Obsolete", "package_type": "VSSOP", "package_code": "DGS", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "DGS0010A", "marking": "53015", "pin_count": 10, "length": 3.1, "width": 3.1, "height": 1.1, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS53015", "order_device": "TPS53015DGSR", "status": "Active", "package_type": "VSSOP", "package_code": "DGS", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "package_drawing_code": "DGS0010A", "marking": "53015", "pin_count": 10, "length": 3.1, "width": 3.1, "height": 1.1, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "未找到"}, {"part_number": "TPS53015", "order_device": "TPS53015DGSR.A", "status": "Active", "package_type": "VSSOP", "package_code": "DGS", "carrier_description": "LARGE T&R", "carrier_quantity": 2500, "package_drawing_code": "DGS0010A", "marking": "53015", "pin_count": 10, "length": 3.1, "width": 3.1, "height": 1.1, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "未找到", "application_grade": "未找到"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS53015", "package_type": "DGS (VSSOP)", "pins": [{"pin_number": "1", "pin_name": "VFB", "pin_description": "D-CAP2反馈输入。通过电阻分压器连接到输出电压。"}, {"pin_number": "2", "pin_name": "PG", "pin_description": "开漏电源良好输出。"}, {"pin_number": "3", "pin_name": "VREG5", "pin_description": "5-V线性稳压器的输出和MOSFET驱动器的电源。用最小4.7μF高质量陶瓷电容旁路到GND。当EN为高电平时，VREG5有效。"}, {"pin_number": "4", "pin_name": "EN", "pin_description": "使能。拉高以使能转换器。"}, {"pin_number": "5", "pin_name": "VIN", "pin_description": "5-V线性稳压器的电源输入。用最小0.1μF高质量陶瓷电容旁路到GND。"}, {"pin_number": "6", "pin_name": "PGND", "pin_description": "系统地。"}, {"pin_number": "7", "pin_name": "DRVL", "pin_description": "低侧N沟道MOSFET栅极驱动器输出。PGND参考的驱动器在PGND(OFF)和VREG5(ON)之间切换。"}, {"pin_number": "8", "pin_name": "SW", "pin_description": "高侧驱动器和过流比较器的开关节点连接。"}, {"pin_number": "9", "pin_name": "DRVH", "pin_description": "高侧N沟道MOSFET栅极驱动器输出。SW参考的驱动器在SW(OFF)和VBST(ON)之间切换。"}, {"pin_number": "10", "pin_name": "VBST", "pin_description": "高侧MOSFET栅极驱动器自举电压输入。在VBST和SW之间连接一个电容。VREG5和VBST之间连接一个内部二极管。"}]}], "datasheet_cn": {"datasheet_name": "ZHCSA32C-JULY 2012-REVISED APRIL 2019", "datasheet_path": "用户上传文件", "release_date": "2019-04", "version": "ZHCSA32C"}, "datasheet_en": {"datasheet_name": "SLVSBF0", "datasheet_path": "未找到", "release_date": "未找到", "version": "SLVSBF0"}, "family_comparison": "未找到", "power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": 28, "min_input_voltage": 4.5, "max_output_voltage": 7, "min_output_voltage": 0.77, "max_output_current": "不适用", "max_switch_frequency": 0.5, "quiescent_current": 660, "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Yes", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": 1, "output_reference_voltage": 0.773, "loop_control_mode": "固定导通时间控制", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "28V", "min_input_voltage": "4.5V", "max_output_voltage": "7V", "min_output_voltage": "0.77V", "max_output_current": "不适用", "max_switch_frequency": "0.5MHz", "quiescent_current": "660µA", "high_side_mosfet_resistance": "不适用", "low_side_mosfet_resistance": "不适用", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Eco-Mode", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Latch", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.773V", "loop_control_mode": "固定导通时间控制"}}