{"part_number": "TPS51113", "manufacturer": "Texas Instruments", "country": "美国", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC转换器", "category_lv3": "降压(<PERSON>)控制器", "part_number_title": "Synchronous Buck Controller with High-Current Gate Driver", "features": ["Flexible Power Rails: 5 V to 12 V", "Reference: 800 mV ± 0.8%", "Voltage Mode Control", "Support Pre-biased Startup", "Programmable Overcurrent Protection with Low-Side RDS(on) Current Sensing", "Fixed 300-kHz (TPS51113) and 600-kHz (TPS51163) Switching Frequency", "UV/OV Protections and Power Good Indicator", "Internal Soft-start", "Integrated High-Current Drivers Powered by VDD", "10-Pin 3 x 3 SON Package"], "description": "The TPS51113 and TPS51163 are cost-optimized, feature rich, single-channel synchronous-buck controllers that operates from a single 4.5-V to 13.2-V supply and can convert an input voltage as low as 1.5 V. The controller implements voltage mode control with a fixed 300-kHz (TPS51113) and 600-kHz (TPS51163) switching frequency. The overcurrent (OC) protection employs the low-side RDS(on) current sensing and has user-programmable threshold. The OC threshold is set by the resistor from LDRV_OC pin to GND. The resistor value is read when the over-current programming circuit applies 10 µA of current to the LDRV_OC pin during the calibration phase of the start-up sequence. The TPS51113/TPS51163 also supports output pre-biased startup. The integrated gate driver is directly powered by VDD. VDD can be connected to VIN in some applications. The strong gate drivers with low deadtime allow for the utilization of larger MOSFETs to achieve higher efficiency. An adaptive anti-cross conduction scheme is used to prevent shoot-through between the power FETs.", "applications": ["Server and Desktop Computer Subsystem Power Supplies (MCH, IOCH, PCI, Termination)", "Distributed Power Supplies", "General DC-DC Converters"], "ordering_information": [{"part_number": "TPS51113", "order_device": "TPS51113DRCR", "status": "Active", "package_type": "VSON", "package_code": "DRC", "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": 3000, "package_drawing_code": "DRC", "marking": "未找到", "pin_count": 10, "length": 3.1, "width": 3.1, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "可调", "application_grade": "Industrial"}, {"part_number": "TPS51113", "order_device": "TPS51113DRCT", "status": "Active", "package_type": "VSON", "package_code": "DRC", "carrier_description": "Ta<PERSON> and Reel", "carrier_quantity": 250, "package_drawing_code": "DRC", "marking": "未找到", "pin_count": 10, "length": 3.1, "width": 3.1, "height": 1.0, "pitch": 0.5, "min_operation_temp": -40, "max_operation_temp": 85, "output_voltage": "可调", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "TPS51113/TPS51163", "package_type": "SON", "pins": [{"pin_number": "1", "pin_name": "BOOT", "pin_description": "Gate drive voltage for the high-side N-channel MOSFET. Typically, a 100 nF capacitor must be connected between this pin and SW. Also, a diode from VDD to BOOT should be externally provided."}, {"pin_number": "2", "pin_name": "SW", "pin_description": "Sense line for the adaptive anti-cross conduction circuitry. Serves as common connection for the flying high-side FET driver."}, {"pin_number": "3", "pin_name": "HDRV", "pin_description": "Gate drive output for the high-side N-channel MOSFET."}, {"pin_number": "4", "pin_name": "LDRV_OC", "pin_description": "Gate drive output for the low-side or rectifier MOSFET. The set point is read during start up calibration with the 10 μA current source present."}, {"pin_number": "5", "pin_name": "GND", "pin_description": "Common reference for the device."}, {"pin_number": "6", "pin_name": "VDD", "pin_description": "Power input to the controller, 4.5 V to 13.2 V."}, {"pin_number": "7", "pin_name": "COMP_EN", "pin_description": "Output of the error amplifier and the shutdown pin. Pulling the voltage on this pin lower than 800 mV shuts the controller down."}, {"pin_number": "8", "pin_name": "FB", "pin_description": "Inverting input to the error amplifier. In normal operation, the voltage on this pin is equal to the internal reference voltage of 800 mV."}, {"pin_number": "9", "pin_name": "VOS", "pin_description": "Input to set undervoltage and overvoltage protections. Undervoltage protection occurs when VOS voltage is lower than 600 mV. The controller shuts down with both MOSFETs latched off. Overvoltage protection occurs when VOS voltage is higher than 1V, the upper MOSFET is turned off and the lower MOSFET is forced on until VOS voltage reaches 400 mV. Then the lower MOSFET is also turned off. After the undervoltage or overvoltage events, normal operation can be restored only by cycling the VDD voltage."}, {"pin_number": "10", "pin_name": "PGOOD", "pin_description": "Open drain power good output. An external pull-up resistor is required."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "TPS51113, TPS51163", "datasheet_path": "slus864.pdf", "release_date": "MAY 2009", "version": "SLUS864"}, "family_comparison": "未找到", "attributes": {"power_mos_integrated": "外置(控制器)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "13.2V", "min_input_voltage": "4.5V", "max_output_voltage": "Adjustable", "min_output_voltage": "Adjustable", "max_output_current": "依赖外部元件", "max_switch_frequency": "330kHz", "quiescent_current": "30uA", "high_side_mosfet_resistance": "不适用(外置MOSFET)", "low_side_mosfet_resistance": "不适用(外置MOSFET)", "over_current_protection_threshold": "外部可调", "operation_mode": "同步", "output_voltage_config_method": "外部反馈电阻", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "无", "power_good_indicator": "Yes", "soft_start": "内部集成", "input_over_voltage_protection": "无", "input_under_voltage_protection": "Latch", "output_over_voltage_protection": "Latch", "output_under_voltage_protection": "Latch", "output_over_load_protection": "Latch", "output_short_circuit_protection": "Latch", "over_temperature_protection": "无", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±0.8%", "output_reference_voltage": "0.8V", "loop_control_mode": "Voltage Mode"}}