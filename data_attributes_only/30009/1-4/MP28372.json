{"part_number": "MP28372", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "NRFND (Not Recommended for New Designs)", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压(<PERSON>)芯片", "part_number_title": "Dual 1.5A, 23V, 1.4MHz Step-Down Converter", "features": ["1.5A Current for Each Output", "0.18Ω Internal Power MOSFET Switches", "Stable with Low ESR Output Ceramic Capacitors", "Up to 90% Efficiency", "40μA Shutdown Mode", "Fixed 1.4MHz Frequency", "Thermal Shutdown", "Cycle-by-Cycle Over Current Protection", "Wide 4.75V to 23V Operating Input Range", "Each Output Adjustable from 0.92V to 16V", "Configurable for Single Output with Double the Current", "Programmable Under Voltage Lockout", "Programmable Soft-Start", "Available in TSSOP20 with Exposed Pad and SOIC Packages"], "description": "The MP28372 is a dual monolithic step-down switch mode converter with built-in internal power MOSFETs. It achieves 1.5A continuous output current for each output over a wide input supply range with excellent load and line regulation. Current mode operation provides fast transient response and eases loop stabilization. Fault condition protection includes cycle-by-cycle current limiting and thermal shutdown. In shutdown mode, the regulator draws 40μA of supply current. The MP28372 requires a minimum number of readily available standard external components.", "applications": ["Distributed Power Systems", "I/O and Core supplies", "DSL Modems", "Set top boxes", "Cable Modems"], "ordering_information": [{"part_number": "MP28372", "order_device": "MP28372DF", "status": "NRFND", "package_type": "TSSOP20F", "package_code": "TSSOP20F", "carrier_description": "<PERSON><PERSON>", "pin_count": 20, "length": 6.6, "width": 4.5, "height": 1.2, "pitch": 0.65, "min_operation_temp": -40, "max_operation_temp": 85, "application_grade": "Industrial"}, {"part_number": "MP28372", "order_device": "MP28372DF-Z", "status": "NRFND", "package_type": "TSSOP20F", "package_code": "TSSOP20F", "carrier_description": "Tape & Reel", "pin_count": 20, "length": 6.6, "width": 4.5, "height": 1.2, "pitch": 0.65, "min_operation_temp": -40, "max_operation_temp": 85, "application_grade": "Industrial"}, {"part_number": "MP28372", "order_device": "MP28372DS", "status": "NRFND", "package_type": "SOIC16", "package_code": "SOIC16", "carrier_description": "<PERSON><PERSON>", "package_drawing_code": "MS-001, VARIATION BB", "pin_count": 16, "length": 10.16, "width": 6.6, "pitch": 2.54, "min_operation_temp": -40, "max_operation_temp": 85, "application_grade": "Industrial"}, {"part_number": "MP28372", "order_device": "MP28372DS-Z", "status": "NRFND", "package_type": "SOIC16", "package_code": "SOIC16", "carrier_description": "Tape & Reel", "package_drawing_code": "MS-001, VARIATION BB", "pin_count": 16, "length": 10.16, "width": 6.6, "pitch": 2.54, "min_operation_temp": -40, "max_operation_temp": 85, "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MP28372", "package_type": "TSSOP20F", "pins": [{"pin_number": "1", "pin_name": "SSA", "pin_description": "Soft-Start Control for Channel A. 9kΩ output resistance from the pin. Set RC time constant with external capacitor for soft start ramp time. Ramp Time = 2.2 x 9kΩ x C."}, {"pin_number": "2", "pin_name": "NC", "pin_description": "No Connect"}, {"pin_number": "3", "pin_name": "BSA", "pin_description": "High-Side Driver <PERSON><PERSON>. Connect a 10nF capacitor from this pin to SWA."}, {"pin_number": "4", "pin_name": "INA", "pin_description": "Supply Voltage Channel A. The MP28372 operates from a +4.75V to +23V unregulated input. Input Ceramic Capacitors should be close to this pin."}, {"pin_number": "5", "pin_name": "SWA", "pin_description": "Switch Channel A. This connects the inductor to either INA through M1A or to PGA through M2A."}, {"pin_number": "6", "pin_name": "PGA", "pin_description": "Power Ground Channel A. This is the Power Ground Connection to the input capacitor ground."}, {"pin_number": "7", "pin_name": "SGA", "pin_description": "Signal Ground Channel A. This pin is the signal ground reference for the regulated output voltage. For this reason care must be taken in its layout. This node should be placed outside of the D1 to C1 ground path to prevent switching current spikes from inducing voltage noise into the part."}, {"pin_number": "8", "pin_name": "FBB", "pin_description": "Feedback Voltage for Channel B. This pin is the feedback voltage. The output voltage is ratio scaled through a voltage divider, and the center point of the divider is connected to this pin. The voltage is compared to the on board 0.92V reference."}, {"pin_number": "9", "pin_name": "COMPB", "pin_description": "Compensation Channel B. This is the output of the transconductance error amplifier. A series RC is placed on this pin for proper control loop compensation."}, {"pin_number": "10", "pin_name": "ENB", "pin_description": "Enable/UVLO Channel B. A voltage greater than 2.62V enables operation. Leave ENB unconnected for automatic startup. An Under Voltage Lockout (UVLO) function can be implemented by the addition of a resistor divider from VIN to GND. For complete low current shutdown the ENB pin voltage needs to be less than 700mV."}, {"pin_number": "11", "pin_name": "SSB", "pin_description": "Soft-Start Control for Channel B. 9kΩ output resistance from the pin. Set RC time constant with external capacitor for soft start ramp time. Ramp Time = 2.2x9kΩx C."}, {"pin_number": "12", "pin_name": "BSB", "pin_description": "High-Side Driver Bo<PERSON> Pin. Connect a 10nF capacitor from this pin to SWB."}, {"pin_number": "13", "pin_name": "NC", "pin_description": "No Connect."}, {"pin_number": "14", "pin_name": "INB", "pin_description": "Supply Voltage Channel B. The MP28372 operates from a +4.75V to +23V unregulated input. Input Ceramic Capacitors should be close to this pin."}, {"pin_number": "15", "pin_name": "SWB", "pin_description": "Switch Channel B. This connects the inductor to either INB through M1B or to PGB through M2B."}, {"pin_number": "16", "pin_name": "PGB", "pin_description": "Power Ground Channel B. This is the Power Ground Connection to the input capacitor ground."}, {"pin_number": "17", "pin_name": "SGB", "pin_description": "Signal Ground Channel B. This pin is the signal ground reference for the regulated output voltage. For this reason care must be taken in its layout. This node should be placed outside of the D1 to C1 ground path to prevent switching current spikes from inducing voltage noise into the part."}, {"pin_number": "18", "pin_name": "FBA", "pin_description": "Feedback Voltage for Channel A. This pin is the feedback voltage. The output voltage is ratio scaled through a voltage divider, and the center point of the divider is connected to this pin. The voltage is compared to the on board 0.92V reference."}, {"pin_number": "19", "pin_name": "COMPA", "pin_description": "Compensation Channel A. This is the output of the transconductance error amplifier. A series RC is placed on this pin for proper control loop compensation."}, {"pin_number": "20", "pin_name": "ENA", "pin_description": "Enable/UVLO Channel A. A voltage greater than 2.62V enables operation. Leave ENA unconnected for automatic startup. An Under Voltage Lockout (UVLO) function can be implemented by the addition of a resistor divider from VIN to GND. For complete low current shutdown the ENA pin voltage needs to be less than 700mV."}]}, {"product_part_number": "MP28372", "package_type": "SOIC16", "pins": [{"pin_number": "1", "pin_name": "SSA", "pin_description": "Soft-Start Control for Channel A. 9kΩ output resistance from the pin. Set RC time constant with external capacitor for soft start ramp time. Ramp Time = 2.2 x 9kΩ x C."}, {"pin_number": "2", "pin_name": "BSA", "pin_description": "High-Side Driver <PERSON><PERSON>. Connect a 10nF capacitor from this pin to SWA."}, {"pin_number": "3", "pin_name": "INA", "pin_description": "Supply Voltage Channel A. The MP28372 operates from a +4.75V to +23V unregulated input. Input Ceramic Capacitors should be close to this pin."}, {"pin_number": "4", "pin_name": "SWA", "pin_description": "Switch Channel A. This connects the inductor to either INA through M1A or to PGA through M2A."}, {"pin_number": "5", "pin_name": "GNDA", "pin_description": "Ground A."}, {"pin_number": "6", "pin_name": "FBB", "pin_description": "Feedback Voltage for Channel B. This pin is the feedback voltage. The output voltage is ratio scaled through a voltage divider, and the center point of the divider is connected to this pin. The voltage is compared to the on board 0.92V reference."}, {"pin_number": "7", "pin_name": "COMPB", "pin_description": "Compensation Channel B. This is the output of the transconductance error amplifier. A series RC is placed on this pin for proper control loop compensation."}, {"pin_number": "8", "pin_name": "ENB", "pin_description": "Enable/UVLO Channel B. A voltage greater than 2.62V enables operation. Leave ENB unconnected for automatic startup. An Under Voltage Lockout (UVLO) function can be implemented by the addition of a resistor divider from VIN to GND. For complete low current shutdown the ENB pin voltage needs to be less than 700mV."}, {"pin_number": "9", "pin_name": "SSB", "pin_description": "Soft-Start Control for Channel B. 9kΩ output resistance from the pin. Set RC time constant with external capacitor for soft start ramp time. Ramp Time = 2.2x9kΩx C."}, {"pin_number": "10", "pin_name": "BSB", "pin_description": "High-Side Driver Bo<PERSON> Pin. Connect a 10nF capacitor from this pin to SWB."}, {"pin_number": "11", "pin_name": "INB", "pin_description": "Supply Voltage Channel B. The MP28372 operates from a +4.75V to +23V unregulated input. Input Ceramic Capacitors should be close to this pin."}, {"pin_number": "12", "pin_name": "SWB", "pin_description": "Switch Channel B. This connects the inductor to either INB through M1B or to PGB through M2B."}, {"pin_number": "13", "pin_name": "GNDB", "pin_description": "Ground B."}, {"pin_number": "14", "pin_name": "FBA", "pin_description": "Feedback Voltage for Channel A. This pin is the feedback voltage. The output voltage is ratio scaled through a voltage divider, and the center point of the divider is connected to this pin. The voltage is compared to the on board 0.92V reference."}, {"pin_number": "15", "pin_name": "COMPA", "pin_description": "Compensation Channel A. This is the output of the transconductance error amplifier. A series RC is placed on this pin for proper control loop compensation."}, {"pin_number": "16", "pin_name": "ENA", "pin_description": "Enable/UVLO Channel A. A voltage greater than 2.62V enables operation. Leave ENA unconnected for automatic startup. An Under Voltage Lockout (UVLO) function can be implemented by the addition of a resistor divider from VIN to GND. For complete low current shutdown the ENA pin voltage needs to be less than 700mV."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MP28372.pdf", "datasheet_path": "local", "release_date": "2007-12-10", "version": "1.4"}, "family_comparison": "NOT RECOMMENDED FOR NEW DESIGNS, REFER TO MP2223", "attributes": {"power_mos_integrated": "单管集成(异步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 2, "max_input_voltage": "23V", "min_input_voltage": "4.75V", "max_output_voltage": "16V", "min_output_voltage": "0.92V", "max_output_current": "1.5A", "max_switch_frequency": "1.4MHz", "quiescent_current": "2400µA", "high_side_mosfet_resistance": "180mΩ", "low_side_mosfet_resistance": "不适用(异步产品)", "over_current_protection_threshold": "3A", "operation_mode": "异步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "未找到", "power_good_indicator": "No", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "No", "output_over_load_protection": "Current Limit", "output_short_circuit_protection": "Fold Back", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "No", "output_discharge": "No", "integrated_ldo": "No", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "2.07%", "output_reference_voltage": "0.92V", "loop_control_mode": "峰值电流模式"}, "extraction_metadata": {"extractor": "prompt_pdf_optimized", "extraction_date": "2024-05-23T08:15:00Z", "pdf_hash": "[REDACTED]"}}