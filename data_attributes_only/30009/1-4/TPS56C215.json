{"part_number": "TPS56C215", "manufacturer": "Texas Instruments", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Automotive", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压芯片 (<PERSON>)", "part_number_title": "TPS56C215 3.8V 至 17V 输入、12A 同步降压 SWIFT™ 转换器", "features": ["集成 13.5mΩ 和 4.5mΩ MOSFET", "支持 12A 持续 IOUT", "4.5V 启动（没有 5.0V 外部偏置）", "整个温度范围内的基准电压为 0.6V ±1%", "0.6V 至 5.5V 输出电压范围", "支持陶瓷输出电容器", "D-CAP3™ 控制模式，用于快速瞬态响应", "可选强制持续导通模式 (FCCM)，用于实现窄输出电压纹波，或自动跳跃 Eco-Mode，用于实现高轻负载效率", "400kHz、800kHz 和 1.2MHz 的可选 Fsw", "单调启动至预偏置输出", "具有断续重启功能的两个可调节电流限制设置", "可选 5V 外部偏置，可提升效率", "可调节软启动，默认软启动时间为 1ms", "-40℃ 至 150℃ 的工作结温范围", "小型 3.5mm x 3.5mm HotRod™ QFN 封装", "与 8A TPS568215 之间引脚对引脚兼容", "在 WEBENCH® 设计工具中受支持"], "description": "TPS56C215 是一款具有自适应导通时间 D-CAP3 控制模式的小尺寸单片 12A 同步降压转换器。该器件集成了低 RDs(on) 功率 MOSFET，简单易用且高效，只需极少的外部组件，适合空间受限的电源系统。具有竞争力的特性包括非常精确的基准电压、快速负载瞬态响应、自动跳跃模式运行以实现轻负载效率、可调节的电流限制和无需外部补偿。强制持续导通模式有助于满足高性能 DSP 和 FPGA 的严格电压调节精度要求。TPS56C215 采用热增强型 18 引脚 HotRod QFN 封装，并且设计为在 -40℃ 至 150℃ 的结温范围内运行。", "applications": ["服务器、云计算、存储", "电信和网络、负载点 (POL)", "IPC、工厂自动化、PLC、测试测量", "高端 DTV"], "ordering_information": [{"part_number": "TPS56C215", "order_device": "TPS56C215RNNR", "status": "Active", "package_type": "VQFN-HR", "package_code": "RNN", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RNN0018A", "marking": "56C215", "pin_count": "18", "length": "3.5", "width": "3.5", "height": "1", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "Automotive"}, {"part_number": "TPS56C215", "order_device": "TPS56C215RNNR.A", "status": "Active", "package_type": "VQFN-HR", "package_code": "RNN", "carrier_description": "LARGE T&R", "carrier_quantity": "3000", "package_drawing_code": "RNN0018A", "marking": "56C215", "pin_count": "18", "length": "3.5", "width": "3.5", "height": "1", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "Automotive"}, {"part_number": "TPS56C215", "order_device": "TPS56C215RNNT", "status": "Active", "package_type": "VQFN-HR", "package_code": "RNN", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RNN0018A", "marking": "56C215", "pin_count": "18", "length": "3.5", "width": "3.5", "height": "1", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "Automotive"}, {"part_number": "TPS56C215", "order_device": "TPS56C215RNNT.A", "status": "Active", "package_type": "VQFN-HR", "package_code": "RNN", "carrier_description": "SMALL T&R", "carrier_quantity": "250", "package_drawing_code": "RNN0018A", "marking": "56C215", "pin_count": "18", "length": "3.5", "width": "3.5", "height": "1", "pitch": "0.5", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "未找到", "application_grade": "Automotive"}], "typical_application_circuit": "Page 20, 图 7-1. Application Schematic", "pin_config": "Page 3, 图 4-1. <PERSON><PERSON><PERSON> Package, 18-Pin VQFN", "function_block_diagram": "Page 13, 6.2 Functional Block Diagram", "pin_function": [{"product_part_number": "TPS56C215", "package_type": "VQFN-HR", "pins": [{"pin_number": "1", "pin_name": "BOOT", "pin_description": "高侧 MOSFET 栅极驱动电压的电源输入。在 BOOT 和 SW 之间连接自举电容器。"}, {"pin_number": "2, 11", "pin_name": "VIN", "pin_description": "控制电路的输入电压电源引脚。在 VIN 和 PGND 之间连接输入去耦电容器。"}, {"pin_number": "3, 4, 5, 8, 9, 10", "pin_name": "PGND", "pin_description": "控制器电路和内部电路的功率 GND 端子。通过短走线连接到 AGND。"}, {"pin_number": "6, 7", "pin_name": "SW", "pin_description": "开关节点端子。将输出电感器连接到此引脚。"}, {"pin_number": "12", "pin_name": "AGND", "pin_description": "内部模拟电路的接地。通过短走线连接到 PGND 平面。"}, {"pin_number": "13", "pin_name": "FB", "pin_description": "转换器反馈输入。连接到输出电压和 AGND 之间的电阻分压器的中心抽头。"}, {"pin_number": "14", "pin_name": "SS", "pin_description": "软启动时间选择引脚。连接外部电容器可设置软启动时间，如果未连接外部电容器，转换器将在 1 ms 内启动。"}, {"pin_number": "15", "pin_name": "EN", "pin_description": "使能输入控制，将此引脚悬空可使能转换器。它还可用于通过连接到 VIN 和 EN 之间的电阻分压器的中心抽头来调整输入 UVLO。"}, {"pin_number": "16", "pin_name": "PGOOD", "pin_description": "开漏电源良好指示器，如果输出电压超出 PGOOD 阈值、过压，或者器件处于热关断、EN 关断或软启动期间，则该指示器被置为低电平。"}, {"pin_number": "17", "pin_name": "VREG5", "pin_description": "4.7V 内部 LDO 输出，也可以由 5V 输入从外部驱动。此引脚为内部电路和栅极驱动器供电。使用 4.7µF 电容器旁路此引脚。"}, {"pin_number": "18", "pin_name": "MODE", "pin_description": "开关频率、电流限制选择和轻负载工作模式选择引脚。将此引脚连接到 VREG5 和 AGND 之间的电阻分压器，以实现表 6-3 中所示的不同 MODE 选项。"}]}], "datasheet_cn": {"datasheet_name": "TPS56C215", "datasheet_path": "ZHCSEU8G", "release_date": "2024-08", "version": "G"}, "datasheet_en": {"datasheet_name": "TPS56C215", "datasheet_path": "SLVSD05", "release_date": "未找到", "version": "未找到"}, "family_comparison": "与 8A TPS568215 之间引脚对引脚兼容", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "17V", "min_input_voltage": "3.8V", "max_output_voltage": "5.5V", "min_output_voltage": "0.6V", "max_output_current": "12A", "max_switch_frequency": "1.2MHz", "quiescent_current": "600µA", "high_side_mosfet_resistance": "13.5mΩ", "low_side_mosfet_resistance": "4.5mΩ", "over_current_protection_threshold": "11.5-13.8A(可调)", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "Eco-Mode", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "Auto Recovery", "output_over_voltage_protection": "Auto Recovery", "output_under_voltage_protection": "Hiccup", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "No", "output_discharge": "Yes", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±1%", "output_reference_voltage": "0.6V", "loop_control_mode": "D-CAP3 (Adaptive On-Time)"}}