{"part_number": "MP2269", "manufacturer": "Monolithic Power Systems", "country": "未找到", "manufacturing_status": "Active", "application_grade": "Industrial", "category_lv1": "电源管理芯片", "category_lv2": "DC-DC开关稳压器", "category_lv3": "降压稳压器", "part_number_title": "3.3V - 30V, 1A, 12μΑ IQ, Synchronous, Step-Down Converter with External Soft Start and Power Good in 2x3mm QFN Package", "features": ["Wide 3.3V to 30V Operating Voltage Range", "1A Continuous Output Current", "1μΑ Low Shutdown Supply Current", "12µA Sleep Mode Quiescent Current", "180mΩ/80mΩ High-Side/Low-Side RDS(ON) for Internal Power MOSFETS", "350kHz to 2.5MHz Programmable Switching Frequency", "Power Good Output", "External Soft Start", "80ns Minimum On Time", "Selectable Forced PWM Mode and Auto PFM/PWM Mode", "Low Dropout Mode", "Hiccup Over-Current Protection (OCP)", "Available in a QFN-15 (2mmx3mm) Package"], "description": "The MP2269 is a frequency-programmable (350kHz to 2.5MHz), synchronous, step-down, switching regulator with integrated, internal, high-side and low-side power MOSFETs. The MP2269 provides 1A of highly efficient output current with current-mode control for fast loop response. The wide 3.3V to 30V input range accommodates a variety of step-down applications. A 1µA shutdown mode quiescent current allows the MP2269 to be used in battery-powered applications. High power conversion efficiency over a wide load range is achieved by scaling down the switching frequency at light-load condition to reduce switching and gate driving losses. An open-drain power good signal indicates the output signal. Frequency foldback helps prevent inductor current runaway during start-up. Thermal shutdown provides reliable and fault-tolerant operation. High duty cycle and low drop-out mode are provided for battery-powered systems. The MP2269 is available in a QFN-15 (2mmx3mm) package.", "applications": ["Battery-Powered Systems", "Smart Homes", "Wide Input Range Power Supplies", "Standby Power Supplies"], "ordering_information": [{"part_number": "MP2269", "order_device": "MP2269GD", "status": "Active", "package_type": "QFN-15", "package_code": "QFN-15 (2mmx3mm)", "carrier_description": "未找到", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "AUH", "pin_count": "15", "length": "3", "width": "2", "height": "1.00", "pitch": "0.40", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Industrial"}, {"part_number": "MP2269", "order_device": "MP2269GD-Z", "status": "Active", "package_type": "QFN-15", "package_code": "QFN-15 (2mmx3mm)", "carrier_description": "Tape & Reel", "carrier_quantity": "未找到", "package_drawing_code": "MO-220", "marking": "AUH", "pin_count": "15", "length": "3", "width": "2", "height": "1.00", "pitch": "0.40", "min_operation_temp": "-40", "max_operation_temp": "125", "output_voltage": "Adjustable", "application_grade": "Industrial"}], "typical_application_circuit": "Yes", "pin_config": "Yes", "function_block_diagram": "Yes", "pin_function": [{"product_part_number": "MP2269", "package_type": "QFN-15 (2mmx3mm)", "pins": [{"pin_number": "1", "pin_name": "MODE", "pin_description": "Mode selection. Pull MODE low or float MODE to set auto PFM/PWM mode; pull MODE high to set forced PWM mode. MODE is pulled down internally. Select MODE before the part starts up."}, {"pin_number": "2", "pin_name": "VIN", "pin_description": "Input supply. VIN supplies power to all the internal control circuitries and the power switch connected to SW. A decoupling capacitor to ground must be placed close to VIN to minimize switching spikes."}, {"pin_number": "3, 8", "pin_name": "PGND", "pin_description": "Power ground."}, {"pin_number": "4", "pin_name": "EN", "pin_description": "Enable. Pull EN below the specified threshold to shut down the MP2269. Pull EN up above the specified threshold to enable the MP2269."}, {"pin_number": "5", "pin_name": "NC", "pin_description": "No connection. Leave NC floating."}, {"pin_number": "6", "pin_name": "PG", "pin_description": "Power good output. The output of PG is an open drain."}, {"pin_number": "7", "pin_name": "BIAS", "pin_description": "Bias input. BIAS must be connected to GND if the bias function is not being used."}, {"pin_number": "9", "pin_name": "SW", "pin_description": "Switch node. SW is the output of the internal power switch."}, {"pin_number": "10", "pin_name": "BST", "pin_description": "Bootstrap. BST is the positive power supply for the high-side MOSFET driver connected to SW. Connect a bypass capacitor between BST and SW."}, {"pin_number": "11", "pin_name": "VCC", "pin_description": "Internal LDO output. VCC supplies power to the internal control circuit and gate drivers. A decoupling capacitor to ground is required close to VCC."}, {"pin_number": "12", "pin_name": "AGND", "pin_description": "Analog ground."}, {"pin_number": "13", "pin_name": "SS", "pin_description": "Soft-start input. Place a capacitor from SS to GND to set the soft-start period. The MP2269 sources 10µA from SS to the soft-start capacitor during start-up. As the SS voltage rises, the feedback threshold voltage increases to limit inrush current during start-up."}, {"pin_number": "14", "pin_name": "FB", "pin_description": "Feedback input. Connect FB to the center point of the external resistor divider. The feedback threshold voltage is 0.8V."}, {"pin_number": "15", "pin_name": "FREQ", "pin_description": "Switching frequency set. Connect a resistor from FREQ to ground to set the switching frequency."}]}], "datasheet_cn": "未找到", "datasheet_en": {"datasheet_name": "MP2269 Datasheet", "datasheet_path": "用户上传文件", "release_date": "2019-03-05", "version": "1.02"}, "family_comparison": "未找到", "power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": "1", "channel_count": "1", "max_input_voltage": "30", "min_input_voltage": "3.3", "max_output_voltage": "未找到", "min_output_voltage": "0.8", "max_output_current": "1", "max_switch_frequency": "2.5", "quiescent_current": "12", "high_side_mosfet_resistance": "180", "low_side_mosfet_resistance": "80", "over_current_protection_threshold": "2.5", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "外部可调", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "OVP", "output_under_voltage_protection": "UVLO", "output_over_load_protection": "Hiccup", "output_short_circuit_protection": "Hiccup", "over_temperature_protection": "Thermal Shutdown", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "2", "output_reference_voltage": "0.8", "loop_control_mode": "峰值电流模式", "attributes": {"power_mos_integrated": "双管集成(同步)", "power_device_type": "MOSFET", "power_phase_number": 1, "channel_count": 1, "max_input_voltage": "30V", "min_input_voltage": "3.3V", "max_output_voltage": "未找到", "min_output_voltage": "0.8V", "max_output_current": "1A", "max_switch_frequency": "2.5MHz", "quiescent_current": "12µA", "high_side_mosfet_resistance": "180mΩ", "low_side_mosfet_resistance": "80mΩ", "over_current_protection_threshold": "2.5A", "operation_mode": "同步", "output_voltage_config_method": "可调", "communication_interface": "无", "enable_function": "Yes", "light_load_mode": "PFM/PWM", "power_good_indicator": "Yes", "soft_start": "External", "input_over_voltage_protection": "No", "input_under_voltage_protection": "UVLO", "output_over_voltage_protection": "No", "output_under_voltage_protection": "Auto Recovery", "output_over_load_protection": "Auto Recovery", "output_short_circuit_protection": "Auto Recovery", "over_temperature_protection": "Auto Recovery", "hundred_percent_duty_cycle": "Yes", "output_discharge": "No", "integrated_ldo": "Yes", "frequency_synchronization": "No", "output_voltage_tracking": "No", "dynamic_voltage_setting": "No", "output_voltage_accuracy": "±2%", "output_reference_voltage": "0.8V", "loop_control_mode": "峰值电流模式"}}